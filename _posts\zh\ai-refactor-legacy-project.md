---
featured: true
featuredOrder: 1
featuredReason: "分享使用AI重构旧项目的实战经验，探索AI在软件工程中的颠覆性力量。"
title: '用AI重构公司老项目的踩坑记录'
excerpt: '最近尝试用Claude和Cursor重构了一个运行10年的老系统，过程比想象中复杂很多。记录一下这次折腾的经历，有成功也有失败，希望对大家有帮助。'
coverImage: '/assets/blog/21.jpeg'
date: "2025-07-09"
lastModified: "2025-07-09"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
---

## 背景：又是那个让人头疼的老项目

说起来有点尴尬，我们公司有个叫"Phoenix"的项目，2015年上线的，用的还是Spring 3.x + JSP那套。这些年业务需求不断加，代码越堆越乱，现在已经是个标准的"屎山"了。

上个月老板突然说要加个新功能，涉及到用户权限和订单流程的改动。我看了看代码...好家伙，一个Controller方法800多行，SQL直接写在JSP里，连个注释都没有。按正常流程估算，至少得3个月才能搞定。

正好最近AI工具挺火的，我就想试试能不能用AI来帮忙重构。反正横竖都是死，不如死马当活马医。

## 第一周：让AI读代码，结果翻车了

一开始我想得很简单，直接把代码丢给Claude，让它帮我分析。结果发现几个问题：

1. **代码太多了** - 整个项目20多万行，Claude根本处理不过来，经常超出token限制
2. **上下文丢失** - 分段分析的话，AI经常理解错业务逻辑
3. **JSP混合代码** - Java代码和HTML混在一起，AI经常搞混

后来同事推荐了Cursor，说是专门针对代码库优化的。试了一下确实好一些，至少能理解项目结构了。

但还是有问题，比如这段代码：

```java
// UserController.java 第156行
if(user.getType().equals("1") && order.getStatus() == 2 && 
   (System.currentTimeMillis() - order.getCreateTime().getTime()) > 86400000) {
    // 一堆业务逻辑...
}
```

我问AI这段是什么意思，它说是"VIP用户在订单创建24小时后的特殊处理"。听起来很合理，但实际上这里的"1"代表的是普通用户，"2"是已支付状态。AI完全理解反了。

**教训1：AI理解代码逻辑没问题，但对业务含义的理解还是要人来把关。**

## 第二周：小步快跑，逐个击破

吃了第一周的亏，我改变了策略。不再让AI一次性分析整个项目，而是：

1. **先梳理核心流程** - 手动画出主要的业务流程图
2. **分模块处理** - 一次只让AI处理一个模块
3. **写测试用例** - 让AI帮我写单元测试，确保重构不出错

这个策略效果好多了。比如重构用户登录模块：

```java
// 原来的代码（简化版）
public String login(HttpServletRequest request) {
    String username = request.getParameter("username");
    String password = request.getParameter("password");
    // 200行的验证逻辑...
    // 直接操作session和数据库...
    return "success";
}
```

我让AI帮我重构成：

```java
@PostMapping("/login")
public ResponseEntity<LoginResponse> login(@RequestBody LoginRequest request) {
    try {
        User user = userService.authenticate(request.getUsername(), request.getPassword());
        String token = jwtService.generateToken(user);
        return ResponseEntity.ok(new LoginResponse(token, user.getId()));
    } catch (AuthenticationException e) {
        return ResponseEntity.status(401).body(new LoginResponse(null, null));
    }
}
```

这样一步步来，效果还不错。但也遇到了新问题：

**AI生成的代码风格不统一** - 有时候用Optional，有时候直接返回null；有时候抛异常，有时候返回错误码。需要我不断地调教和统一。

## 第三周：踩坑与收获并存

到了第三周，基本摸清了AI的脾气。总结几个经验：

### 什么AI做得好：
- **代码格式化和重构** - 把长方法拆分，提取公共逻辑，这些AI做得很棒
- **生成测试用例** - 给个方法签名，AI能生成覆盖各种边界情况的测试
- **代码注释** - AI写的注释比我们自己写的还详细

### 什么AI做不好：
- **复杂业务判断** - 涉及到业务规则的地方，AI经常理解错
- **性能优化** - AI倾向于写"正确"的代码，但不一定是"高效"的代码
- **架构设计** - 大的架构决策还是需要人来做

### 一个具体的例子

有个订单状态更新的方法，原来是这样的：

```java
public void updateOrderStatus(Long orderId, Integer status) {
    Order order = orderDao.findById(orderId);
    if (status == 1) {
        // 待支付逻辑
    } else if (status == 2) {
        // 已支付逻辑
        // 发送短信
        // 更新库存
        // 记录日志
    } else if (status == 3) {
        // 已发货逻辑
    }
    // ... 还有7-8个状态
}
```

我让AI重构，它给出了状态模式的实现，代码结构确实清晰了很多。但有个问题：原来的代码在状态2的时候会发送短信，AI重构后这个逻辑丢了。

我问AI为什么，它说"发送短信不应该在订单状态更新中处理，应该用事件驱动"。从技术角度讲没错，但现实是我们没有消息队列，短信服务也是同步调用的。

**教训2：AI给出的往往是"理想"的解决方案，但现实项目有各种约束，需要人来平衡。**

## 最终结果：有得有失

经过三周的折腾，最终的结果是：

### 成功的地方：
- **代码可读性大幅提升** - 原来800行的方法拆成了10几个小方法
- **测试覆盖率从0%提升到70%** - AI生成的测试用例质量还不错
- **修复了几个潜在bug** - AI在重构过程中发现了一些边界情况的问题

### 不太理想的地方：
- **时间成本** - 虽然AI写代码快，但调教AI、验证结果、修复问题的时间也不少
- **性能略有下降** - 重构后的代码更规范了，但某些地方性能有所下降
- **团队学习成本** - 其他同事需要时间适应新的代码结构

### 数据对比：
- 重构前：核心接口平均响应时间 650ms，每周线上bug 3-5个
- 重构后：核心接口平均响应时间 720ms，每周线上bug 1-2个

没有想象中那么戏剧化的提升，但确实有改善。

## 一些思考

这次经历让我对AI辅助开发有了更现实的认识：

1. **AI是工具，不是银弹** - 它能提高效率，但不能解决所有问题
2. **人机协作是关键** - AI负责执行，人负责决策和把关
3. **渐进式改进更靠谱** - 一口吃成胖子容易消化不良

对于想尝试AI重构的同学，我的建议是：
- 从小模块开始，不要一上来就想重构整个系统
- 一定要写测试用例，这是安全网
- 多花时间理解AI的输出，不要盲目信任
- 保持现实的期望，AI能帮忙但不是万能的

总的来说，这次尝试还是值得的。虽然没有"3天重构3个月工作量"那么夸张，但确实让我们的代码质量有了提升，也让团队对AI工具有了更深入的了解。

下次再遇到类似的项目，我会更有信心用AI来辅助开发了。
