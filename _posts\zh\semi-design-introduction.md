---
excerpt: "最近在项目中用了Semi Design，感觉还不错。记录一下使用体验和一些踩坑心得，顺便聊聊这个抖音团队开源的UI库有什么特别的地方。"
coverImage: "/assets/blog/25.png"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
featured: true
featuredOrder: 1
featuredReason: "现代化 React UI 组件库深度解析"
title: "抖音开源神器 Semi Design：3秒设计稿转代码，10万+开发者的选择"
date: "2025-07-19"
lastModified: "2025-07-19"
---

最近公司新项目要选 UI 库，之前一直用 Ant Design，但这次想试试别的。听说抖音开源了个 Semi Design，看起来还挺有意思的，就拿来试了试。用了一段时间，感觉确实有些亮点，也踩了一些坑，记录一下。

## 初印象：还挺不错的

简单来说，Semi Design 就是抖音团队开源的一套 React UI 组件库。不过和其他 UI 库不太一样的是，它更注重设计和开发的协作。

说实话，刚开始我也是抱着试试看的心态，毕竟 Ant Design 用习惯了。但用了几天下来，发现 Semi 确实有些独特的地方：

1. **组件质量不错** - 基本的组件该有的都有，而且样式比较现代
2. **文档写得还行** - 至少比一些开源项目的文档要清楚
3. **主题定制比较灵活** - 这个后面会详细说

当然也有一些问题，比如生态还不如 Ant Design 那么成熟，有些第三方插件可能找不到。

## 比较有意思的几个特性

### 设计稿转代码

这个功能我觉得挺有意思的，虽然还没在实际项目中用过。据说可以从 Figma 设计稿直接生成代码，几秒钟就能搞定。

我试了一下 demo，确实能生成一些简单的布局代码。不过说实话，生成的代码质量还是有限的，复杂的业务逻辑肯定还是要手写。但对于快速原型开发来说，这个功能还是挺实用的。

### 架构设计

Semi 的架构设计还挺有意思的，用了 FA（Foundation & Adapter）模式。简单来说就是把核心逻辑和 UI 层分离，这样理论上可以比较容易地迁移到其他框架。

不过对于普通开发者来说，这个可能感知不强。主要还是对库的维护者比较有意义。

## 无障碍支持做得还不错

这个我必须要夸一下，Semi 在无障碍支持方面确实下了功夫。基本的键盘导航、焦点管理这些都有，而且大部分组件都能用屏幕阅读器正常访问。

之前用其他一些 UI 库的时候，经常遇到键盘导航有问题的情况，Semi 这方面做得比较好。虽然可能平时感知不强，但对于有无障碍需求的项目来说，这个还是很重要的。

## 国际化支持

如果你的项目需要支持多语言，Semi 的国际化做得还可以：

- 内置了常见语言的翻译
- 支持 RTL 布局（阿拉伯语、希伯来语那种从右到左的）
- 时区处理也比较完善

不过说实话，大部分国内项目可能用不到这些功能。但如果你要做海外项目，这些确实能省不少事。

## 主题定制：这个真的很香

这个是我觉得 Semi 最大的亮点之一。主题定制做得确实不错，比 Ant Design 灵活多了。

### 主题编辑器很好用

Semi 提供了一个在线的主题编辑器，可以实时预览效果。我试了一下，改个颜色、字体大小什么的，立马就能看到效果。而且可以导出主题包，直接在项目里用。

之前用 Ant Design 的时候，要改个主题色还得折腾半天，各种 less 变量配置。Semi 这个就简单多了，点点鼠标就搞定。

### 官方主题还挺多

Semi 官方提供了好几套主题：

- 默认的飞书主题（比较商务风）
- 抖音创作服务的主题（比较年轻化）
- 剪映的主题（偏创意风格）

我们项目最后选了抖音创作服务的主题，感觉比较符合我们产品的调性。

## 一些技术细节

### SSR 支持

我们项目用的 Next.js，Semi 在 SSR 方面没什么问题。之前用一些其他 UI 库的时候，经常遇到服务端渲染报错的情况，Semi 这方面还是比较稳定的。

### 组件质量

用下来感觉组件的质量还不错，基本没遇到什么明显的 bug。而且更新也比较频繁，有问题的话修复得也挺快。

不过有一点要注意，Semi 的一些组件 API 和 Ant Design 不太一样，如果你之前用惯了 Ant Design，可能需要适应一下。

## 实际使用中的一些感受

### 踩过的坑

说说我在使用过程中遇到的一些问题：

1. **生态不够成熟** - 相比 Ant Design，Semi 的第三方插件和扩展还是少一些。比如我想找个日期范围选择器的增强版本，就没找到合适的。

2. **API 差异** - 有些组件的 API 和 Ant Design 不太一样，迁移的时候需要改一些代码。不过这个也不算大问题，改改就好了。

3. **文档有时候不够详细** - 有些高级用法的文档写得不够详细，需要去看源码才能搞清楚。

### 比较满意的地方

当然，优点还是挺多的：

1. **主题定制真的很方便** - 这个前面说过了，确实比其他 UI 库方便很多。

2. **组件设计比较现代** - 默认的样式比 Ant Design 要现代一些，不需要太多定制就能用。

3. **性能还不错** - 打包体积控制得还可以，而且组件的渲染性能也没什么问题。

## 想试试的话

如果你也想试试 Semi Design，安装很简单：

```bash
npm install @douyinfe/semi-ui
```

然后就可以开始用了：

```jsx
import { Button, Toast } from "@douyinfe/semi-ui";

function App() {
  return <Button onClick={() => Toast.success("Hello Semi!")}>点击我</Button>;
}
```

建议先去官网看看 demo，感受一下组件的样式和交互。如果觉得合适，再考虑在项目中使用。

## 总结一下

用了一段时间 Semi Design，总体感觉还是不错的。特别是主题定制这块，确实比其他 UI 库方便很多。如果你的项目对主题定制有比较高的要求，或者想尝试一些新的 UI 库，Semi Design 值得试试。

当然，如果你的项目已经用 Ant Design 用得很顺手，而且没有特殊需求，那也没必要强行切换。毕竟 Ant Design 的生态还是更成熟一些。

不过对于新项目来说，Semi Design 确实是一个不错的选择。至少在我们的项目中，用起来还是挺顺手的。

有兴趣的话可以去[Semi Design 官网](https://semi.design/)看看，先试试 demo 再决定要不要用。
