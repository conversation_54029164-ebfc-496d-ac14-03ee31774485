"use client";

import { useState } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import { Search, CheckCircle, XCircle, User, Calendar, MapPin, Copy, AlertTriangle } from "lucide-react";

// 身份证信息接口
interface IdCardInfo {
  isValid: boolean;
  province: string;
  city: string;
  county: string;
  birthDate: string;
  age: number;
  gender: string;
  constellation: string;
  zodiac: string;
  errors: string[];
}

// 地区代码映射
const REGION_CODES: { [key: string]: { province: string; city?: string; county?: string } } = {
  "110000": { province: "北京市" },
  "110100": { province: "北京市", city: "北京市" },
  "110101": { province: "北京市", city: "北京市", county: "东城区" },
  "110102": { province: "北京市", city: "北京市", county: "西城区" },
  "110105": { province: "北京市", city: "北京市", county: "朝阳区" },
  "110106": { province: "北京市", city: "北京市", county: "丰台区" },
  "110107": { province: "北京市", city: "北京市", county: "石景山区" },
  "110108": { province: "北京市", city: "北京市", county: "海淀区" },
  "110109": { province: "北京市", city: "北京市", county: "门头沟区" },
  "110111": { province: "北京市", city: "北京市", county: "房山区" },
  "110112": { province: "北京市", city: "北京市", county: "通州区" },
  "110113": { province: "北京市", city: "北京市", county: "顺义区" },
  "110114": { province: "北京市", city: "北京市", county: "昌平区" },
  "110115": { province: "北京市", city: "北京市", county: "大兴区" },
  "110116": { province: "北京市", city: "北京市", county: "怀柔区" },
  "110117": { province: "北京市", city: "北京市", county: "平谷区" },
  "110118": { province: "北京市", city: "北京市", county: "密云区" },
  "110119": { province: "北京市", city: "北京市", county: "延庆区" },
  
  "120000": { province: "天津市" },
  "120100": { province: "天津市", city: "天津市" },
  "120101": { province: "天津市", city: "天津市", county: "和平区" },
  "120102": { province: "天津市", city: "天津市", county: "河东区" },
  "120103": { province: "天津市", city: "天津市", county: "河西区" },
  "120104": { province: "天津市", city: "天津市", county: "南开区" },
  "120105": { province: "天津市", city: "天津市", county: "河北区" },
  "120106": { province: "天津市", city: "天津市", county: "红桥区" },
  "120110": { province: "天津市", city: "天津市", county: "东丽区" },
  "120111": { province: "天津市", city: "天津市", county: "西青区" },
  "120112": { province: "天津市", city: "天津市", county: "津南区" },
  "120113": { province: "天津市", city: "天津市", county: "北辰区" },
  "120114": { province: "天津市", city: "天津市", county: "武清区" },
  "120115": { province: "天津市", city: "天津市", county: "宝坻区" },
  "120116": { province: "天津市", city: "天津市", county: "滨海新区" },
  "120117": { province: "天津市", city: "天津市", county: "宁河区" },
  "120118": { province: "天津市", city: "天津市", county: "静海区" },
  "120119": { province: "天津市", city: "天津市", county: "蓟州区" },
  
  "130000": { province: "河北省" },
  "130100": { province: "河北省", city: "石家庄市" },
  "130200": { province: "河北省", city: "唐山市" },
  "130300": { province: "河北省", city: "秦皇岛市" },
  "130400": { province: "河北省", city: "邯郸市" },
  "130500": { province: "河北省", city: "邢台市" },
  "130600": { province: "河北省", city: "保定市" },
  "130700": { province: "河北省", city: "张家口市" },
  "130800": { province: "河北省", city: "承德市" },
  "130900": { province: "河北省", city: "沧州市" },
  "131000": { province: "河北省", city: "廊坊市" },
  "131100": { province: "河北省", city: "衡水市" },
  
  "140000": { province: "山西省" },
  "140100": { province: "山西省", city: "太原市" },
  "140200": { province: "山西省", city: "大同市" },
  "140300": { province: "山西省", city: "阳泉市" },
  "140400": { province: "山西省", city: "长治市" },
  "140500": { province: "山西省", city: "晋城市" },
  "140600": { province: "山西省", city: "朔州市" },
  "140700": { province: "山西省", city: "晋中市" },
  "140800": { province: "山西省", city: "运城市" },
  "140900": { province: "山西省", city: "忻州市" },
  "141000": { province: "山西省", city: "临汾市" },
  "141100": { province: "山西省", city: "吕梁市" },
  
  "150000": { province: "内蒙古自治区" },
  "150100": { province: "内蒙古自治区", city: "呼和浩特市" },
  "150200": { province: "内蒙古自治区", city: "包头市" },
  "150300": { province: "内蒙古自治区", city: "乌海市" },
  "150400": { province: "内蒙古自治区", city: "赤峰市" },
  "150500": { province: "内蒙古自治区", city: "通辽市" },
  "150600": { province: "内蒙古自治区", city: "鄂尔多斯市" },
  "150700": { province: "内蒙古自治区", city: "呼伦贝尔市" },
  "150800": { province: "内蒙古自治区", city: "巴彦淖尔市" },
  "150900": { province: "内蒙古自治区", city: "乌兰察布市" },
  "152200": { province: "内蒙古自治区", city: "兴安盟" },
  "152500": { province: "内蒙古自治区", city: "锡林郭勒盟" },
  "152900": { province: "内蒙古自治区", city: "阿拉善盟" },
  
  "210000": { province: "辽宁省" },
  "210100": { province: "辽宁省", city: "沈阳市" },
  "210200": { province: "辽宁省", city: "大连市" },
  "210300": { province: "辽宁省", city: "鞍山市" },
  "210400": { province: "辽宁省", city: "抚顺市" },
  "210500": { province: "辽宁省", city: "本溪市" },
  "210600": { province: "辽宁省", city: "丹东市" },
  "210700": { province: "辽宁省", city: "锦州市" },
  "210800": { province: "辽宁省", city: "营口市" },
  "210900": { province: "辽宁省", city: "阜新市" },
  "211000": { province: "辽宁省", city: "辽阳市" },
  "211100": { province: "辽宁省", city: "盘锦市" },
  "211200": { province: "辽宁省", city: "铁岭市" },
  "211300": { province: "辽宁省", city: "朝阳市" },
  "211400": { province: "辽宁省", city: "葫芦岛市" },
  
  "220000": { province: "吉林省" },
  "220100": { province: "吉林省", city: "长春市" },
  "220200": { province: "吉林省", city: "吉林市" },
  "220300": { province: "吉林省", city: "四平市" },
  "220400": { province: "吉林省", city: "辽源市" },
  "220500": { province: "吉林省", city: "通化市" },
  "220600": { province: "吉林省", city: "白山市" },
  "220700": { province: "吉林省", city: "松原市" },
  "220800": { province: "吉林省", city: "白城市" },
  "222400": { province: "吉林省", city: "延边朝鲜族自治州" },
  
  "230000": { province: "黑龙江省" },
  "230100": { province: "黑龙江省", city: "哈尔滨市" },
  "230200": { province: "黑龙江省", city: "齐齐哈尔市" },
  "230300": { province: "黑龙江省", city: "鸡西市" },
  "230400": { province: "黑龙江省", city: "鹤岗市" },
  "230500": { province: "黑龙江省", city: "双鸭山市" },
  "230600": { province: "黑龙江省", city: "大庆市" },
  "230700": { province: "黑龙江省", city: "伊春市" },
  "230800": { province: "黑龙江省", city: "佳木斯市" },
  "230900": { province: "黑龙江省", city: "七台河市" },
  "231000": { province: "黑龙江省", city: "牡丹江市" },
  "231100": { province: "黑龙江省", city: "黑河市" },
  "231200": { province: "黑龙江省", city: "绥化市" },
  "232700": { province: "黑龙江省", city: "大兴安岭地区" },
  
  "310000": { province: "上海市" },
  "310100": { province: "上海市", city: "上海市" },
  "310101": { province: "上海市", city: "上海市", county: "黄浦区" },
  "310104": { province: "上海市", city: "上海市", county: "徐汇区" },
  "310105": { province: "上海市", city: "上海市", county: "长宁区" },
  "310106": { province: "上海市", city: "上海市", county: "静安区" },
  "310107": { province: "上海市", city: "上海市", county: "普陀区" },
  "310109": { province: "上海市", city: "上海市", county: "虹口区" },
  "310110": { province: "上海市", city: "上海市", county: "杨浦区" },
  "310112": { province: "上海市", city: "上海市", county: "闵行区" },
  "310113": { province: "上海市", city: "上海市", county: "宝山区" },
  "310114": { province: "上海市", city: "上海市", county: "嘉定区" },
  "310115": { province: "上海市", city: "上海市", county: "浦东新区" },
  "310116": { province: "上海市", city: "上海市", county: "金山区" },
  "310117": { province: "上海市", city: "上海市", county: "松江区" },
  "310118": { province: "上海市", city: "上海市", county: "青浦区" },
  "310120": { province: "上海市", city: "上海市", county: "奉贤区" },
  "310151": { province: "上海市", city: "上海市", county: "崇明区" },
  
  "320000": { province: "江苏省" },
  "320100": { province: "江苏省", city: "南京市" },
  "320200": { province: "江苏省", city: "无锡市" },
  "320300": { province: "江苏省", city: "徐州市" },
  "320400": { province: "江苏省", city: "常州市" },
  "320500": { province: "江苏省", city: "苏州市" },
  "320600": { province: "江苏省", city: "南通市" },
  "320700": { province: "江苏省", city: "连云港市" },
  "320800": { province: "江苏省", city: "淮安市" },
  "320900": { province: "江苏省", city: "盐城市" },
  "321000": { province: "江苏省", city: "扬州市" },
  "321100": { province: "江苏省", city: "镇江市" },
  "321200": { province: "江苏省", city: "泰州市" },
  "321300": { province: "江苏省", city: "宿迁市" },
  
  "330000": { province: "浙江省" },
  "330100": { province: "浙江省", city: "杭州市" },
  "330200": { province: "浙江省", city: "宁波市" },
  "330300": { province: "浙江省", city: "温州市" },
  "330400": { province: "浙江省", city: "嘉兴市" },
  "330500": { province: "浙江省", city: "湖州市" },
  "330600": { province: "浙江省", city: "绍兴市" },
  "330700": { province: "浙江省", city: "金华市" },
  "330800": { province: "浙江省", city: "衢州市" },
  "330900": { province: "浙江省", city: "舟山市" },
  "331000": { province: "浙江省", city: "台州市" },
  "331100": { province: "浙江省", city: "丽水市" },
  
  "340000": { province: "安徽省" },
  "340100": { province: "安徽省", city: "合肥市" },
  "340200": { province: "安徽省", city: "芜湖市" },
  "340300": { province: "安徽省", city: "蚌埠市" },
  "340400": { province: "安徽省", city: "淮南市" },
  "340500": { province: "安徽省", city: "马鞍山市" },
  "340600": { province: "安徽省", city: "淮北市" },
  "340700": { province: "安徽省", city: "铜陵市" },
  "340800": { province: "安徽省", city: "安庆市" },
  "341000": { province: "安徽省", city: "黄山市" },
  "341100": { province: "安徽省", city: "滁州市" },
  "341200": { province: "安徽省", city: "阜阳市" },
  "341300": { province: "安徽省", city: "宿州市" },
  "341500": { province: "安徽省", city: "六安市" },
  "341600": { province: "安徽省", city: "亳州市" },
  "341700": { province: "安徽省", city: "池州市" },
  "341800": { province: "安徽省", city: "宣城市" },
  
  "350000": { province: "福建省" },
  "350100": { province: "福建省", city: "福州市" },
  "350200": { province: "福建省", city: "厦门市" },
  "350300": { province: "福建省", city: "莆田市" },
  "350400": { province: "福建省", city: "三明市" },
  "350500": { province: "福建省", city: "泉州市" },
  "350600": { province: "福建省", city: "漳州市" },
  "350700": { province: "福建省", city: "南平市" },
  "350800": { province: "福建省", city: "龙岩市" },
  "350900": { province: "福建省", city: "宁德市" },
  
  "360000": { province: "江西省" },
  "360100": { province: "江西省", city: "南昌市" },
  "360200": { province: "江西省", city: "景德镇市" },
  "360300": { province: "江西省", city: "萍乡市" },
  "360400": { province: "江西省", city: "九江市" },
  "360500": { province: "江西省", city: "新余市" },
  "360600": { province: "江西省", city: "鹰潭市" },
  "360700": { province: "江西省", city: "赣州市" },
  "360800": { province: "江西省", city: "吉安市" },
  "360900": { province: "江西省", city: "宜春市" },
  "361000": { province: "江西省", city: "抚州市" },
  "361100": { province: "江西省", city: "上饶市" },
  
  "370000": { province: "山东省" },
  "370100": { province: "山东省", city: "济南市" },
  "370200": { province: "山东省", city: "青岛市" },
  "370300": { province: "山东省", city: "淄博市" },
  "370400": { province: "山东省", city: "枣庄市" },
  "370500": { province: "山东省", city: "东营市" },
  "370600": { province: "山东省", city: "烟台市" },
  "370700": { province: "山东省", city: "潍坊市" },
  "370800": { province: "山东省", city: "济宁市" },
  "370900": { province: "山东省", city: "泰安市" },
  "371000": { province: "山东省", city: "威海市" },
  "371100": { province: "山东省", city: "日照市" },
  "371200": { province: "山东省", city: "莱芜市" },
  "371300": { province: "山东省", city: "临沂市" },
  "371400": { province: "山东省", city: "德州市" },
  "371500": { province: "山东省", city: "聊城市" },
  "371600": { province: "山东省", city: "滨州市" },
  "371700": { province: "山东省", city: "菏泽市" },
  
  "410000": { province: "河南省" },
  "410100": { province: "河南省", city: "郑州市" },
  "410200": { province: "河南省", city: "开封市" },
  "410300": { province: "河南省", city: "洛阳市" },
  "410400": { province: "河南省", city: "平顶山市" },
  "410500": { province: "河南省", city: "安阳市" },
  "410600": { province: "河南省", city: "鹤壁市" },
  "410700": { province: "河南省", city: "新乡市" },
  "410800": { province: "河南省", city: "焦作市" },
  "410900": { province: "河南省", city: "濮阳市" },
  "411000": { province: "河南省", city: "许昌市" },
  "411100": { province: "河南省", city: "漯河市" },
  "411200": { province: "河南省", city: "三门峡市" },
  "411300": { province: "河南省", city: "南阳市" },
  "411400": { province: "河南省", city: "商丘市" },
  "411500": { province: "河南省", city: "信阳市" },
  "411600": { province: "河南省", city: "周口市" },
  "411700": { province: "河南省", city: "驻马店市" },
  "419001": { province: "河南省", city: "济源市" },
  
  "420000": { province: "湖北省" },
  "420100": { province: "湖北省", city: "武汉市" },
  "420200": { province: "湖北省", city: "黄石市" },
  "420300": { province: "湖北省", city: "十堰市" },
  "420500": { province: "湖北省", city: "宜昌市" },
  "420600": { province: "湖北省", city: "襄阳市" },
  "420700": { province: "湖北省", city: "鄂州市" },
  "420800": { province: "湖北省", city: "荆门市" },
  "420900": { province: "湖北省", city: "孝感市" },
  "421000": { province: "湖北省", city: "荆州市" },
  "421100": { province: "湖北省", city: "黄冈市" },
  "421200": { province: "湖北省", city: "咸宁市" },
  "421300": { province: "湖北省", city: "随州市" },
  "422800": { province: "湖北省", city: "恩施土家族苗族自治州" },
  "429004": { province: "湖北省", city: "仙桃市" },
  "429005": { province: "湖北省", city: "潜江市" },
  "429006": { province: "湖北省", city: "天门市" },
  "429021": { province: "湖北省", city: "神农架林区" },
  
  "430000": { province: "湖南省" },
  "430100": { province: "湖南省", city: "长沙市" },
  "430200": { province: "湖南省", city: "株洲市" },
  "430300": { province: "湖南省", city: "湘潭市" },
  "430400": { province: "湖南省", city: "衡阳市" },
  "430500": { province: "湖南省", city: "邵阳市" },
  "430600": { province: "湖南省", city: "岳阳市" },
  "430700": { province: "湖南省", city: "常德市" },
  "430800": { province: "湖南省", city: "张家界市" },
  "430900": { province: "湖南省", city: "益阳市" },
  "431000": { province: "湖南省", city: "郴州市" },
  "431100": { province: "湖南省", city: "永州市" },
  "431200": { province: "湖南省", city: "怀化市" },
  "431300": { province: "湖南省", city: "娄底市" },
  "433100": { province: "湖南省", city: "湘西土家族苗族自治州" },
  
  "440000": { province: "广东省" },
  "440100": { province: "广东省", city: "广州市" },
  "440200": { province: "广东省", city: "韶关市" },
  "440300": { province: "广东省", city: "深圳市" },
  "440400": { province: "广东省", city: "珠海市" },
  "440500": { province: "广东省", city: "汕头市" },
  "440600": { province: "广东省", city: "佛山市" },
  "440700": { province: "广东省", city: "江门市" },
  "440800": { province: "广东省", city: "湛江市" },
  "440900": { province: "广东省", city: "茂名市" },
  "441200": { province: "广东省", city: "肇庆市" },
  "441300": { province: "广东省", city: "惠州市" },
  "441400": { province: "广东省", city: "梅州市" },
  "441500": { province: "广东省", city: "汕尾市" },
  "441600": { province: "广东省", city: "河源市" },
  "441700": { province: "广东省", city: "阳江市" },
  "441800": { province: "广东省", city: "清远市" },
  "441900": { province: "广东省", city: "东莞市" },
  "442000": { province: "广东省", city: "中山市" },
  "445100": { province: "广东省", city: "潮州市" },
  "445200": { province: "广东省", city: "揭阳市" },
  "445300": { province: "广东省", city: "云浮市" },
  
  "450000": { province: "广西壮族自治区" },
  "450100": { province: "广西壮族自治区", city: "南宁市" },
  "450200": { province: "广西壮族自治区", city: "柳州市" },
  "450300": { province: "广西壮族自治区", city: "桂林市" },
  "450400": { province: "广西壮族自治区", city: "梧州市" },
  "450500": { province: "广西壮族自治区", city: "北海市" },
  "450600": { province: "广西壮族自治区", city: "防城港市" },
  "450700": { province: "广西壮族自治区", city: "钦州市" },
  "450800": { province: "广西壮族自治区", city: "贵港市" },
  "450900": { province: "广西壮族自治区", city: "玉林市" },
  "451000": { province: "广西壮族自治区", city: "百色市" },
  "451100": { province: "广西壮族自治区", city: "贺州市" },
  "451200": { province: "广西壮族自治区", city: "河池市" },
  "451300": { province: "广西壮族自治区", city: "来宾市" },
  "451400": { province: "广西壮族自治区", city: "崇左市" },
  
  "460000": { province: "海南省" },
  "460100": { province: "海南省", city: "海口市" },
  "460200": { province: "海南省", city: "三亚市" },
  "460300": { province: "海南省", city: "三沙市" },
  "460400": { province: "海南省", city: "儋州市" },
  
  "500000": { province: "重庆市" },
  "500100": { province: "重庆市", city: "重庆市" },
  "500101": { province: "重庆市", city: "重庆市", county: "万州区" },
  "500102": { province: "重庆市", city: "重庆市", county: "涪陵区" },
  "500103": { province: "重庆市", city: "重庆市", county: "渝中区" },
  "500104": { province: "重庆市", city: "重庆市", county: "大渡口区" },
  "500105": { province: "重庆市", city: "重庆市", county: "江北区" },
  "500106": { province: "重庆市", city: "重庆市", county: "沙坪坝区" },
  "500107": { province: "重庆市", city: "重庆市", county: "九龙坡区" },
  "500108": { province: "重庆市", city: "重庆市", county: "南岸区" },
  "500109": { province: "重庆市", city: "重庆市", county: "北碚区" },
  "500110": { province: "重庆市", city: "重庆市", county: "綦江区" },
  "500111": { province: "重庆市", city: "重庆市", county: "大足区" },
  "500112": { province: "重庆市", city: "重庆市", county: "渝北区" },
  "500113": { province: "重庆市", city: "重庆市", county: "巴南区" },
  "500114": { province: "重庆市", city: "重庆市", county: "黔江区" },
  "500115": { province: "重庆市", city: "重庆市", county: "长寿区" },
  "500116": { province: "重庆市", city: "重庆市", county: "江津区" },
  "500117": { province: "重庆市", city: "重庆市", county: "合川区" },
  "500118": { province: "重庆市", city: "重庆市", county: "永川区" },
  "500119": { province: "重庆市", city: "重庆市", county: "南川区" },
  "500120": { province: "重庆市", city: "重庆市", county: "璧山区" },
  "500151": { province: "重庆市", city: "重庆市", county: "铜梁区" },
  "500152": { province: "重庆市", city: "重庆市", county: "潼南区" },
  "500153": { province: "重庆市", city: "重庆市", county: "荣昌区" },
  "500154": { province: "重庆市", city: "重庆市", county: "开州区" },
  "500155": { province: "重庆市", city: "重庆市", county: "梁平区" },
  "500156": { province: "重庆市", city: "重庆市", county: "武隆区" },
  
  "510000": { province: "四川省" },
  "510100": { province: "四川省", city: "成都市" },
  "510300": { province: "四川省", city: "自贡市" },
  "510400": { province: "四川省", city: "攀枝花市" },
  "510500": { province: "四川省", city: "泸州市" },
  "510600": { province: "四川省", city: "德阳市" },
  "510700": { province: "四川省", city: "绵阳市" },
  "510800": { province: "四川省", city: "广元市" },
  "510900": { province: "四川省", city: "遂宁市" },
  "511000": { province: "四川省", city: "内江市" },
  "511100": { province: "四川省", city: "乐山市" },
  "511300": { province: "四川省", city: "南充市" },
  "511400": { province: "四川省", city: "眉山市" },
  "511500": { province: "四川省", city: "宜宾市" },
  "511600": { province: "四川省", city: "广安市" },
  "511700": { province: "四川省", city: "达州市" },
  "511800": { province: "四川省", city: "雅安市" },
  "511900": { province: "四川省", city: "巴中市" },
  "512000": { province: "四川省", city: "资阳市" },
  "513200": { province: "四川省", city: "阿坝藏族羌族自治州" },
  "513300": { province: "四川省", city: "甘孜藏族自治州" },
  "513400": { province: "四川省", city: "凉山彝族自治州" },
  
  "520000": { province: "贵州省" },
  "520100": { province: "贵州省", city: "贵阳市" },
  "520200": { province: "贵州省", city: "六盘水市" },
  "520300": { province: "贵州省", city: "遵义市" },
  "520400": { province: "贵州省", city: "安顺市" },
  "520500": { province: "贵州省", city: "毕节市" },
  "520600": { province: "贵州省", city: "铜仁市" },
  "522300": { province: "贵州省", city: "黔西南布依族苗族自治州" },
  "522600": { province: "贵州省", city: "黔东南苗族侗族自治州" },
  "522700": { province: "贵州省", city: "黔南布依族苗族自治州" },
  
  "530000": { province: "云南省" },
  "530100": { province: "云南省", city: "昆明市" },
  "530300": { province: "云南省", city: "曲靖市" },
  "530400": { province: "云南省", city: "玉溪市" },
  "530500": { province: "云南省", city: "保山市" },
  "530600": { province: "云南省", city: "昭通市" },
  "530700": { province: "云南省", city: "丽江市" },
  "530800": { province: "云南省", city: "普洱市" },
  "530900": { province: "云南省", city: "临沧市" },
  "532300": { province: "云南省", city: "楚雄彝族自治州" },
  "532500": { province: "云南省", city: "红河哈尼族彝族自治州" },
  "532600": { province: "云南省", city: "文山壮族苗族自治州" },
  "532800": { province: "云南省", city: "西双版纳傣族自治州" },
  "532900": { province: "云南省", city: "大理白族自治州" },
  "533100": { province: "云南省", city: "德宏傣族景颇族自治州" },
  "533300": { province: "云南省", city: "怒江傈僳族自治州" },
  "533400": { province: "云南省", city: "迪庆藏族自治州" },
  
  "540000": { province: "西藏自治区" },
  "540100": { province: "西藏自治区", city: "拉萨市" },
  "540200": { province: "西藏自治区", city: "日喀则市" },
  "540300": { province: "西藏自治区", city: "昌都市" },
  "540400": { province: "西藏自治区", city: "林芝市" },
  "540500": { province: "西藏自治区", city: "山南市" },
  "540600": { province: "西藏自治区", city: "那曲市" },
  "542500": { province: "西藏自治区", city: "阿里地区" },
  
  "610000": { province: "陕西省" },
  "610100": { province: "陕西省", city: "西安市" },
  "610200": { province: "陕西省", city: "铜川市" },
  "610300": { province: "陕西省", city: "宝鸡市" },
  "610400": { province: "陕西省", city: "咸阳市" },
  "610500": { province: "陕西省", city: "渭南市" },
  "610600": { province: "陕西省", city: "延安市" },
  "610700": { province: "陕西省", city: "汉中市" },
  "610800": { province: "陕西省", city: "榆林市" },
  "610900": { province: "陕西省", city: "安康市" },
  "611000": { province: "陕西省", city: "商洛市" },
  
  "620000": { province: "甘肃省" },
  "620100": { province: "甘肃省", city: "兰州市" },
  "620200": { province: "甘肃省", city: "嘉峪关市" },
  "620300": { province: "甘肃省", city: "金昌市" },
  "620400": { province: "甘肃省", city: "白银市" },
  "620500": { province: "甘肃省", city: "天水市" },
  "620600": { province: "甘肃省", city: "武威市" },
  "620700": { province: "甘肃省", city: "张掖市" },
  "620800": { province: "甘肃省", city: "平凉市" },
  "620900": { province: "甘肃省", city: "酒泉市" },
  "621000": { province: "甘肃省", city: "庆阳市" },
  "621100": { province: "甘肃省", city: "定西市" },
  "621200": { province: "甘肃省", city: "陇南市" },
  "622900": { province: "甘肃省", city: "临夏回族自治州" },
  "623000": { province: "甘肃省", city: "甘南藏族自治州" },
  
  "630000": { province: "青海省" },
  "630100": { province: "青海省", city: "西宁市" },
  "630200": { province: "青海省", city: "海东市" },
  "632200": { province: "青海省", city: "海北藏族自治州" },
  "632300": { province: "青海省", city: "黄南藏族自治州" },
  "632500": { province: "青海省", city: "海南藏族自治州" },
  "632600": { province: "青海省", city: "果洛藏族自治州" },
  "632700": { province: "青海省", city: "玉树藏族自治州" },
  "632800": { province: "青海省", city: "海西蒙古族藏族自治州" },
  
  "640000": { province: "宁夏回族自治区" },
  "640100": { province: "宁夏回族自治区", city: "银川市" },
  "640200": { province: "宁夏回族自治区", city: "石嘴山市" },
  "640300": { province: "宁夏回族自治区", city: "吴忠市" },
  "640400": { province: "宁夏回族自治区", city: "固原市" },
  "640500": { province: "宁夏回族自治区", city: "中卫市" },
  
  "650000": { province: "新疆维吾尔自治区" },
  "650100": { province: "新疆维吾尔自治区", city: "乌鲁木齐市" },
  "650200": { province: "新疆维吾尔自治区", city: "克拉玛依市" },
  "650400": { province: "新疆维吾尔自治区", city: "吐鲁番市" },
  "650500": { province: "新疆维吾尔自治区", city: "哈密市" },
  "652300": { province: "新疆维吾尔自治区", city: "昌吉回族自治州" },
  "652700": { province: "新疆维吾尔自治区", city: "博尔塔拉蒙古自治州" },
  "652800": { province: "新疆维吾尔自治区", city: "巴音郭楞蒙古自治州" },
  "652900": { province: "新疆维吾尔自治区", city: "阿克苏地区" },
  "653000": { province: "新疆维吾尔自治区", city: "克孜勒苏柯尔克孜自治州" },
  "653100": { province: "新疆维吾尔自治区", city: "喀什地区" },
  "653200": { province: "新疆维吾尔自治区", city: "和田地区" },
  "654000": { province: "新疆维吾尔自治区", city: "伊犁哈萨克自治州" },
  "654200": { province: "新疆维吾尔自治区", city: "塔城地区" },
  "654300": { province: "新疆维吾尔自治区", city: "阿勒泰地区" },
  "659001": { province: "新疆维吾尔自治区", city: "石河子市" },
  "659002": { province: "新疆维吾尔自治区", city: "阿拉尔市" },
  "659003": { province: "新疆维吾尔自治区", city: "图木舒克市" },
  "659004": { province: "新疆维吾尔自治区", city: "五家渠市" },
  "659005": { province: "新疆维吾尔自治区", city: "北屯市" },
  "659006": { province: "新疆维吾尔自治区", city: "铁门关市" },
  "659007": { province: "新疆维吾尔自治区", city: "双河市" },
  "659008": { province: "新疆维吾尔自治区", city: "可克达拉市" },
  "659009": { province: "新疆维吾尔自治区", city: "昆玉市" },
  "659010": { province: "新疆维吾尔自治区", city: "胡杨河市" },
  
  "710000": { province: "台湾省" },
  "810000": { province: "香港特别行政区" },
  "820000": { province: "澳门特别行政区" }
};

export default function IdCardValidatorPage() {
  const [idCard, setIdCard] = useState('');
  const [result, setResult] = useState<IdCardInfo | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const t = useTranslations('pages.tools.tools.idCardValidator');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 验证身份证号码
  const validateIdCard = (id: string): IdCardInfo => {
    const errors: string[] = [];
    let isValid = true;
    
    // 基本格式检查
    if (!id) {
      return {
        isValid: false,
        province: '',
        city: '',
        county: '',
        birthDate: '',
        age: 0,
        gender: '',
        constellation: '',
        zodiac: '',
        errors: [t('messages.emptyInput')]
      };
    }

    // 长度检查
    if (id.length !== 18) {
      errors.push(t('messages.lengthError'));
      isValid = false;
    }

    // 格式检查
    const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idPattern.test(id)) {
      errors.push(t('messages.invalidFormat'));
      isValid = false;
    }

    // 提取信息
    const regionCode = id.substring(0, 6);
    const birthYear = parseInt(id.substring(6, 10));
    const birthMonth = parseInt(id.substring(10, 12));
    const birthDay = parseInt(id.substring(12, 14));
    const genderCode = parseInt(id.substring(16, 17));
    
    // 地区验证
    let province = '';
    let city = '';
    let county = '';
    
    // 查找地区信息
    const region = REGION_CODES[regionCode] || 
                  REGION_CODES[regionCode.substring(0, 4) + '00'] || 
                  REGION_CODES[regionCode.substring(0, 2) + '0000'];
    
    if (region) {
      province = region.province;
      city = region.city || '';
      county = region.county || '';
    } else {
      errors.push(t('messages.regionError'));
      isValid = false;
    }

    // 出生日期验证
    const birthDate = new Date(birthYear, birthMonth - 1, birthDay);
    const currentDate = new Date();
    
    if (birthDate.getFullYear() !== birthYear || 
        birthDate.getMonth() !== birthMonth - 1 || 
        birthDate.getDate() !== birthDay) {
      errors.push(t('messages.dateError'));
      isValid = false;
    }
    
    if (birthDate > currentDate) {
      errors.push(t('messages.futureDateError'));
      isValid = false;
    }
    
    if (birthYear < 1900) {
      errors.push(t('messages.yearError'));
      isValid = false;
    }

    // 校验码验证
    if (isValid && id.length === 18) {
      const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      
      let sum = 0;
      for (let i = 0; i < 17; i++) {
        sum += parseInt(id.charAt(i)) * weights[i];
      }
      
      const checkCode = checkCodes[sum % 11];
      if (id.charAt(17).toUpperCase() !== checkCode) {
        errors.push(t('messages.checksumError'));
        isValid = false;
      }
    }

    // 计算年龄
    const age = currentDate.getFullYear() - birthYear - 
                (currentDate.getMonth() < birthMonth - 1 || 
                 (currentDate.getMonth() === birthMonth - 1 && currentDate.getDate() < birthDay) ? 1 : 0);

    // 性别
    const gender = genderCode % 2 === 0 ? t('ui.female') : t('ui.male');

    // 星座
    const constellation = getConstellation(birthMonth, birthDay);

    // 生肖
    const zodiac = getZodiac(birthYear);

    // 格式化出生日期
    const formattedBirthDate = locale === 'zh'
      ? `${birthYear}年${birthMonth.toString().padStart(2, '0')}月${birthDay.toString().padStart(2, '0')}日`
      : `${birthMonth.toString().padStart(2, '0')}/${birthDay.toString().padStart(2, '0')}/${birthYear}`;

    if (isValid) {
      errors.push(t('messages.validationPassed'));
    }

    return {
      isValid,
      province,
      city,
      county,
      birthDate: formattedBirthDate,
      age,
      gender,
      constellation,
      zodiac,
      errors
    };
  };

  // 获取星座
  const getConstellation = (month: number, day: number): string => {
    const constellations = [
      { key: 'aquarius', start: [1, 20], end: [2, 18] },
      { key: 'pisces', start: [2, 19], end: [3, 20] },
      { key: 'aries', start: [3, 21], end: [4, 19] },
      { key: 'taurus', start: [4, 20], end: [5, 20] },
      { key: 'gemini', start: [5, 21], end: [6, 21] },
      { key: 'cancer', start: [6, 22], end: [7, 22] },
      { key: 'leo', start: [7, 23], end: [8, 22] },
      { key: 'virgo', start: [8, 23], end: [9, 22] },
      { key: 'libra', start: [9, 23], end: [10, 23] },
      { key: 'scorpio', start: [10, 24], end: [11, 22] },
      { key: 'sagittarius', start: [11, 23], end: [12, 21] },
      { key: 'capricorn', start: [12, 22], end: [1, 19] }
    ];

    for (const constellation of constellations) {
      const [startMonth, startDay] = constellation.start;
      const [endMonth, endDay] = constellation.end;

      if (startMonth === endMonth) {
        if (month === startMonth && day >= startDay && day <= endDay) {
          return t(`constellations.${constellation.key}`);
        }
      } else {
        if ((month === startMonth && day >= startDay) ||
            (month === endMonth && day <= endDay)) {
          return t(`constellations.${constellation.key}`);
        }
      }
    }

    return t('messages.unknown');
  };

  // 获取生肖
  const getZodiac = (year: number): string => {
    const zodiacs = ['monkey', 'rooster', 'dog', 'pig', 'rat', 'ox', 'tiger', 'rabbit', 'dragon', 'snake', 'horse', 'goat'];
    return t(`zodiac.${zodiacs[year % 12]}`);
  };

  // 处理输入变化
  const handleInputChange = (value: string) => {
    // 只允许数字和X
    const cleanValue = value.replace(/[^0-9Xx]/g, '').toUpperCase();
    setIdCard(cleanValue);
    
    if (cleanValue.length === 18) {
      setIsValidating(true);
      setTimeout(() => {
        const validationResult = validateIdCard(cleanValue);
        setResult(validationResult);
        setIsValidating(false);
      }, 300);
    } else {
      setResult(null);
    }
  };

  // 复制结果
  const copyResult = async () => {
    if (!result) return;
    
    const resultText = `${t('input.label')}：${idCard}
${t('result.title')}：${result.isValid ? t('result.valid') : t('result.invalid')}
${t('ui.region')}：${result.province}${result.city}${result.county}
${t('ui.birthDate')}：${result.birthDate}
${t('ui.age')}：${result.age}${t('ui.years')}
${t('ui.gender')}：${result.gender}
${t('ui.constellation')}：${result.constellation}
${t('ui.zodiac')}：${result.zodiac}`;
    
    try {
      await navigator.clipboard.writeText(resultText);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch {
      console.error(t('messages.copyFailed'));
    }
  };

  // 清空输入
  const clearInput = () => {
    setIdCard('');
    setResult(null);
  };

  // 如果是英文环境，显示说明页面
  if (locale === 'en') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              ✅ Chinese ID Card Validator
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Validate Chinese ID card numbers and extract information
            </p>

            <div className="max-w-2xl mx-auto bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">
                🇨🇳 Chinese-Specific Tool
              </h2>
              <p className="text-green-700 dark:text-green-300 mb-4">
                This tool validates Chinese ID card numbers (身份证号码) and extracts detailed information including
                region, birth date, gender, age, zodiac signs, and more. It follows the 18-digit format used in mainland China.
              </p>
              <p className="text-green-700 dark:text-green-300 mb-4">
                <strong>Validation Features:</strong>
              </p>
              <ul className="text-green-700 dark:text-green-300 mb-6 text-left list-disc list-inside space-y-1">
                <li>Check ID format and length (18 digits)</li>
                <li>Validate region codes against official database</li>
                <li>Verify birth date validity</li>
                <li>Calculate and verify checksum digit</li>
                <li>Extract personal information (age, gender, zodiac)</li>
                <li>Show detailed region information (province/city/county)</li>
              </ul>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-4 mb-6">
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  <strong>📋 Example:</strong> 110101199001011234 → Beijing, Born Jan 1, 1990, Male, Age 34
                </p>
              </div>

              <div className="flex justify-center">
                <Link
                  href="/tools/id-card-validator"
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Use Tool (Chinese Interface)
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": t('title'),
            "description": t('description'),
            "url": `${SITE_URL}/tools/id-card-validator`,
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "All",
            "permissions": "browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
{t('breadcrumb.home')}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href="/tools" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
              </div>
            </li>
          </ol>
        </nav>
        {/* 工具介绍 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
            <Search className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('description')}
          </p>
        </div>

        {/* 输入区域 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('input.label')}
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={idCard}
                  onChange={(e) => handleInputChange(e.target.value)}
                  placeholder={t('input.placeholder')}
                  maxLength={18}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 text-lg font-mono tracking-wider"
                />
                {idCard && (
                  <button
                    onClick={clearInput}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <XCircle className="w-5 h-5" />
                  </button>
                )}
              </div>
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
{t('messages.inputLength')} {idCard.length}/18 位
              </div>
            </div>
          </div>
        </div>

        {/* 验证状态 */}
        {isValidating && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-700 dark:text-blue-300">{t('messages.validating')}</span>
            </div>
          </div>
        )}

        {/* 验证结果 */}
        {result && (
          <div className={`rounded-xl shadow-lg border p-6 mb-6 ${
            result.isValid 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' 
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
          }`}>
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {result.isValid ? (
                  <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                ) : (
                  <XCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
                )}
                <h3 className={`text-lg font-semibold ${
                  result.isValid 
                    ? 'text-green-800 dark:text-green-200' 
                    : 'text-red-800 dark:text-red-200'
                }`}>
                  {result.isValid ? t('messages.validationPassed') : t('messages.validationFailed')}
                </h3>
              </div>
              {result.isValid && (
                <button
                  onClick={copyResult}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-200 rounded-lg hover:bg-green-200 dark:hover:bg-green-700 transition-colors"
                >
                  <Copy className="w-4 h-4" />
                  <span className="text-sm">{copySuccess ? t('messages.copied') : t('messages.copyResult')}</span>
                </button>
              )}
            </div>

            {/* 错误信息 */}
            {!result.isValid && (
              <div className="space-y-2">
                {result.errors.map((error, index) => (
                  <div key={index} className="flex items-center space-x-2 text-red-700 dark:text-red-300">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                  </div>
                ))}
              </div>
            )}

            {/* 详细信息 */}
            {result.isValid && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <div>
                      <div className="text-sm text-green-600 dark:text-green-400">{t('ui.region')}</div>
                      <div className="font-medium text-green-800 dark:text-green-200">
                        {result.province}{result.city}{result.county}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <div>
                      <div className="text-sm text-green-600 dark:text-green-400">{t('ui.birthDate')}</div>
                      <div className="font-medium text-green-800 dark:text-green-200">
                        {result.birthDate}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <User className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <div>
                      <div className="text-sm text-green-600 dark:text-green-400">{t('ui.gender')}</div>
                      <div className="font-medium text-green-800 dark:text-green-200">
                        {result.gender}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-green-600 dark:text-green-400">{t('ui.age')}</div>
                    <div className="font-medium text-green-800 dark:text-green-200">
                      {result.age}{t('ui.years')}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-green-600 dark:text-green-400">{t('ui.constellation')}</div>
                    <div className="font-medium text-green-800 dark:text-green-200">
                      {result.constellation}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-green-600 dark:text-green-400">{t('ui.zodiac')}</div>
                    <div className="font-medium text-green-800 dark:text-green-200">
                      {result.zodiac}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('instructions.title')}
          </h3>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
            <div className="flex items-start space-x-2">
              <span className="text-blue-600 dark:text-blue-400 font-medium">•</span>
              <span>{t('instructions.support18')}</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-600 dark:text-blue-400 font-medium">•</span>
              <span>{t('instructions.autoparse')}</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-600 dark:text-blue-400 font-medium">•</span>
              <span>{t('instructions.checksum')}</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-600 dark:text-blue-400 font-medium">•</span>
              <span>{t('instructions.astrology')}</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-red-600 dark:text-red-400 font-medium">•</span>
              <span>{t('instructions.privacy')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}