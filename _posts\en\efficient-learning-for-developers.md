---
excerpt: "After 5 years of work, I've learned many technologies but always feel like I lack depth. Sharing some learning methods I've figured out, hoping to help fellow anxious programmers."
coverImage: "/assets/blog/0.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
ogImage:
  url: "/assets/blog/0.png"
featured: true
featuredOrder: 1
featuredReason: "Practical programmer learning method sharing"
title: "5 Years of Learning Anxiety: How I Built My Technical Knowledge System"
date: "2025-07-01"
lastModified: "2025-07-01"
---

Recently, I was chatting with some friends and found we all share a common anxiety: technology updates too fast, and we always feel like we can't keep up.

Just got familiar with React, and Vue3 comes out; just figured out <PERSON><PERSON>, and Kubernetes becomes essential; finally mastered some backend frameworks, and AI starts disrupting the entire industry...

I've been working for 5 years, and looking back, I realize I've learned many technologies but always feel like I lack depth. When facing complex problems, I still get confused. After reflection, I found the issue isn't that I haven't learned enough, but that my learning methods were problematic.

Today I'm sharing some learning methods I've figured out over the years, hoping to help fellow anxious programmers.

## Stop Being a "Collector"

Here's a harsh truth: I have over 3000 links in my bookmarks, but I've probably only read through less than 10% of them.

I used to bookmark good articles and add good courses to my cart, resulting in bookmarks gathering dust and files expiring in cloud storage. This "squirrel hoarding" style of learning gave me the illusion that "I'm learning," but I actually learned nothing.

Later I realized that learning isn't about collecting information, but about solving problems.

### My current approach:

**1. Goal-driven learning**

No more aimless learning. Before learning any new technology, I ask myself: What current problem can this technology solve for me?

For example, I only deeply studied Webpack optimization when my project builds were slow; I only researched Redux principles when I encountered state management issues. Knowledge learned this way leaves a particularly deep impression.

**2. Immediate practice**

After reading articles or watching videos, I immediately write a small demo to verify. Even if it's just a few lines of code, the effect of writing it yourself is completely different from just reading.

**3. Teach others**

This method is particularly effective. I often share new technologies I've learned at team meetings or write technical blogs. To explain clearly, I force myself to understand every detail and fill in gaps.

## Build Your Own Learning System

Having the right mindset isn't enough; you also need a systematic approach.

### Information Source Management

I now only follow a few high-quality information sources:

- **Tech blogs**: Ruan Yifeng's weekly newsletter, columns by experts on Juejin
- **GitHub**: Following updates from excellent projects
- **Podcasts**: Listening to tech podcasts during commute
- **Communities**: Hot discussions on V2EX, Juejin

The key is quality over quantity. I'd rather miss some information than be drowned by the information flood.

### 80/20 Rule Learning Method

I've been using this method for a long time, and it's particularly practical:

- **20% time understanding overview**: Quickly browse official documentation to understand what the technology does and what problems it solves
- **80% time deep practice**: Focus on the most core and commonly used features, mastering them through project practice

For example, when learning React, I don't start by studying all Hooks, but first master useState and useEffect to build basic applications.

### Feynman Learning Method in Practice

This method sounds sophisticated, but it's essentially "pretending to teach beginners":

1. **Choose a concept**: Like "what is closure"
2. **Explain in simplest terms**: Imagine you're explaining to a friend who just started programming
3. **Find where you get stuck**: Where you can't explain clearly is your knowledge blind spot
4. **Go back and study**: Re-learn the stuck parts until you can explain fluently

I often use this method to verify whether I truly understand a concept.

## Knowledge Management: My "Second Brain"

Knowledge learned without organization will quickly be forgotten. I spent a lot of time figuring out knowledge management methods.

### Tool Selection

I tried many tools and finally chose Obsidian:

- **Supports Markdown**: Very smooth to write
- **Bidirectional linking**: Can connect related knowledge points
- **Local storage**: No worry about data loss

I use GitHub Gist to manage code snippets, convenient for searching and sharing anytime.

### Classification Method

I organize notes with this structure:

```
Tech Notes/
├── Frontend/
│   ├── React/
│   ├── Vue/
│   └── Engineering/
├── Backend/
│   ├── Node.js/
│   └── Database/
├── Project Experience/
│   ├── Pitfall Records/
│   └── Solutions/
└── Learning Plans/
```

For each technical point, I record:

- **What it is**: Basic concepts
- **Why**: What problems it solves
- **How to use**: Code examples
- **Notes**: Pitfalls I've encountered

### Regular Review

I review my notes every month, and you'll find:

- Some concepts I didn't understand before suddenly make sense
- Some knowledge points can be connected to form a more complete system
- Some outdated information needs updating

Like refactoring code, knowledge systems also need regular refactoring.

## Some Practical Tips

### 1. Keep a Learning Journal

I record what I learned each day, what problems I encountered, and how I solved them. This has two benefits:

- I can see my progress, boosting confidence
- When encountering similar problems, I can quickly find solutions

### 2. Join Tech Communities

Find like-minded friends to learn together, supervise each other, and share with each other. I'm in several tech groups where people often share new tech articles or discuss technical issues.

### 3. Set Learning Goals

Don't make them too grand, like "master TypeScript basics this month" or "understand React Hooks principles this week." With clear goals, learning has more direction.

## Final Thoughts

I've been using this learning method for over two years with good results. Now when I encounter new technologies, I don't get anxious anymore, but follow this process to learn and master them.

Of course, everyone's situation is different, and methods need to be adapted accordingly. The important thing is to find your own learning rhythm and not be held hostage by the speed of technology updates.

Technology is endless to learn, but learning ability can be cultivated. Rather than being anxious about endless new technologies, focus on improving your learning efficiency.

Hope these experiences help you. If you also have good learning methods, welcome to share and exchange!
