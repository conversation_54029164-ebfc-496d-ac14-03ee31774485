---
excerpt: "今年刚好35岁，身边不少朋友都在焦虑所谓的'35岁危机'。分享一下我这些年的思考和实践，如何在这个年龄段找到自己的价值和定位。"
coverImage: "/assets/blog/9.png"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
featured: false
title: "35岁了，我不焦虑了"
date: "2025-07-01"
lastModified: "2025-07-01"
---

今年刚好 35 岁，生日那天朋友圈里一堆人在调侃"35 岁危机"。

说实话，前两年我也焦虑过。看着身边的年轻同事熬夜写代码面不改色，学新技术如饥似渴，而我开始担心体力跟不上，担心被淘汰。

但经过这两年的思考和实践，我发现所谓的"35 岁危机"更多是心理上的恐慌。关键是要找到自己在这个年龄段的价值和定位。

## 我的焦虑从何而来

### 体力确实不如从前

以前能连续熬夜一周，现在熬一晚第二天就废了。看着那些 23、24 岁的小伙子精力充沛，确实会有危机感。

### 学习新技术变慢了

不是学不会，而是学得没以前快了。以前一个新框架，一个周末就能上手，现在需要更多时间去理解和消化。

### 家庭责任重了

有了孩子之后，不可能像以前那样把所有时间都投入到工作中。周末要陪家人，晚上要辅导作业，可支配的时间少了很多。

### 年轻人的冲击

新来的应届生，技术栈比我新，薪资要求比我低，学习能力比我强。有时候真的会想：我还有什么优势？

## 我是怎么调整心态的

### 接受现实，不要硬拼

35 岁了，就别和 25 岁的人比体力和学习速度了。这就像让一个马拉松选手去和短跑选手比 100 米，没意义。

我现在的策略是：**用经验和判断力弥补体力和速度的不足**。

### 重新定义自己的价值

年轻时我的价值是"能干活"，现在我的价值是"能解决问题"。

**以前**：产品经理说要做个功能，我就埋头写代码。
**现在**：产品经理说要做个功能，我会先问为什么要做，有没有更好的解决方案，技术实现的成本和风险是什么。

这种思考能力，是年轻人暂时还不具备的。

## 我这些年做了什么

### 从技术专家到问题解决者

我不再追求掌握所有最新的技术，而是专注于解决业务问题。

**举个例子**：去年公司的订单系统经常出问题，年轻同事总是想着用最新的技术重构。我花了一周时间分析现有系统，发现问题的根源是数据库设计不合理，通过优化几个关键查询就解决了 80%的问题。

这种"四两拨千斤"的能力，是我这个年龄段的优势。

### 从执行者到决策者

我开始更多地参与技术决策，而不只是执行。

- 技术选型时，我会考虑团队的技术水平、项目的时间要求、后期的维护成本
- 架构设计时，我会考虑系统的扩展性、可维护性，而不只是功能实现
- 遇到问题时，我会从业务角度思考，而不只是技术角度

### 从个人贡献者到团队赋能者

我发现，帮助团队成长比自己写代码更有价值。

- 我会主动分享经验，帮助年轻同事避免我踩过的坑
- 我会在代码评审时，不只是指出问题，还会解释为什么这样写不好
- 我会在项目复盘时，总结经验教训，形成团队的最佳实践

## 一些具体的建议

### 建立自己的护城河

**深度比广度更重要**。与其什么都会一点，不如在某个领域做到专家级别。

我选择深耕后端架构，现在团队遇到复杂的系统设计问题，都会来找我讨论。这种不可替代性，就是我的护城河。

### 培养软技能

技术会过时，但沟通能力、项目管理能力、业务理解能力不会。

我这两年刻意练习了：

- **沟通能力**：学会用非技术人员能理解的语言解释技术问题
- **项目管理**：学会评估风险，制定计划，协调资源
- **业务理解**：深入了解公司的商业模式和业务流程

### 建立个人品牌

我开始写技术博客，不是为了炫技，而是为了总结和分享。

写博客的过程中，我发现自己对技术的理解更深了，表达能力也提升了。更重要的是，这让我在行业内有了一定的知名度。

### 保持学习，但要有选择

我不再追逐所有的新技术，而是有选择地学习。

**学习原则**：

- 优先学习能解决当前业务问题的技术
- 关注技术的本质和原理，而不只是语法和 API
- 学习那些有长期价值的技术，而不是昙花一现的热点

## 35 岁的优势

现在回头看，35 岁其实有很多优势：

### 经验丰富

我见过各种各样的坑，知道什么方案可行，什么方案有风险。这种判断力，是年轻人需要时间积累的。

### 心态稳定

不会因为一个技术问题就焦虑，不会因为一次失败就怀疑人生。这种稳定性，对团队来说很重要。

### 视野更广

我不只关注技术，还关注业务、关注用户、关注商业价值。这种全局观，让我能做出更好的决策。

### 沟通能力强

我能和不同背景的人有效沟通，能把复杂的技术问题解释清楚。这在跨部门协作中很重要。

## 写在最后

35 岁不是终点，而是一个新的起点。

关键是要认清自己的位置，发挥自己的优势，而不是和年轻人硬拼体力和学习速度。

我们这个年龄段的价值，不在于写了多少行代码，而在于解决了多少问题，帮助了多少人，创造了多少价值。

如果你也在为"35 岁危机"焦虑，不妨停下来想想：你的经验、你的判断力、你的沟通能力，这些都是年轻人暂时还不具备的优势。

用好这些优势，35 岁的程序员一样可以很有价值。
