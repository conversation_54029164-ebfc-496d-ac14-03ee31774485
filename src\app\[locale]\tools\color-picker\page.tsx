"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_URL } from "@/lib/constants";

interface ColorFormats {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  hsv: { h: number; s: number; v: number };
}

// 预设颜色调色板
const colorPalettes = {
  basic: [
    "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF",
    "#000000", "#FFFFFF", "#808080", "#800000", "#008000", "#000080"
  ],
  material: [
    "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5", "#2196F3",
    "#03A9F4", "#00BCD4", "#009688", "#4CAF50", "#8BC34A", "#CDDC39",
    "#FFEB3B", "#FFC107", "#FF9800", "#FF5722", "#795548", "#9E9E9E"
  ],
  pastel: [
    "#FFB3BA", "#FFDFBA", "#FFFFBA", "#BAFFC9", "#BAE1FF", "#D4BAFF",
    "#FFBAFF", "#FFBABA", "#FFE5BA", "#E5FFBA", "#BAFFE5", "#BAE5FF"
  ]
};

export default function ColorPickerPage() {
  const [currentColor, setCurrentColor] = useState("#3B82F6");
  const [colorFormats, setColorFormats] = useState<ColorFormats>({
    hex: "#3B82F6",
    rgb: { r: 59, g: 130, b: 246 },
    hsl: { h: 217, s: 91, l: 60 },
    hsv: { h: 217, s: 76, v: 96 }
  });
  const [colorHistory, setColorHistory] = useState<string[]>([]);
  const [selectedPalette, setSelectedPalette] = useState<keyof typeof colorPalettes>("material");
  const [inputFormat, setInputFormat] = useState<"hex" | "rgb" | "hsl">("hex");
  const [inputValue, setInputValue] = useState("");
  const [conversionError, setConversionError] = useState("");

  const t = useTranslations('pages.tools.tools.colorPicker');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 颜色格式转换函数
  const hexToRgb = (hex: string): { r: number; g: number; b: number } => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  };

  const rgbToHex = (r: number, g: number, b: number): string => {
    return "#" + [r, g, b].map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  };

  const rgbToHsl = (r: number, g: number, b: number): { h: number; s: number; l: number } => {
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0;
    const l = (max + min) / 2;

    if (max === min) {
      h = s = 0;
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  };

  const rgbToHsv = (r: number, g: number, b: number): { h: number; s: number; v: number } => {
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0;
    const v = max;

    const d = max - min;
    s = max === 0 ? 0 : d / max;

    if (max === min) {
      h = 0;
    } else {
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      v: Math.round(v * 100)
    };
  };

  // HSL转RGB
  const hslToRgb = (h: number, s: number, l: number): { r: number; g: number; b: number } => {
    h /= 360;
    s /= 100;
    l /= 100;

    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  // 解析颜色代码
  const parseColorInput = useCallback((input: string, format: string): string | null => {
    const cleanInput = input.trim();
    
    try {
      if (format === "hex") {
        // 解析HEX格式
        const hexMatch = cleanInput.match(/^#?([a-f\d]{3}|[a-f\d]{6})$/i);
        if (hexMatch) {
          let hex = hexMatch[1];
          if (hex.length === 3) {
            hex = hex.split('').map(char => char + char).join('');
          }
          return "#" + hex.toUpperCase();
        }
      } else if (format === "rgb") {
        // 解析RGB格式
        const rgbMatch = cleanInput.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*[\d.]+)?\s*\)/);
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]);
          const g = parseInt(rgbMatch[2]);
          const b = parseInt(rgbMatch[3]);
          if (r >= 0 && r <= 255 && g >= 0 && g <= 255 && b >= 0 && b <= 255) {
            return rgbToHex(r, g, b);
          }
        }
      } else if (format === "hsl") {
        // 解析HSL格式
        const hslMatch = cleanInput.match(/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(?:,\s*[\d.]+)?\s*\)/);
        if (hslMatch) {
          const h = parseInt(hslMatch[1]);
          const s = parseInt(hslMatch[2]);
          const l = parseInt(hslMatch[3]);
          if (h >= 0 && h <= 360 && s >= 0 && s <= 100 && l >= 0 && l <= 100) {
            const rgb = hslToRgb(h, s, l);
            return rgbToHex(rgb.r, rgb.g, rgb.b);
          }
        }
      }
    } catch {
      return null;
    }
    
    return null;
  }, []);

  // 更新颜色格式
  const updateColorFormats = useCallback((hex: string) => {
    const rgb = hexToRgb(hex);
    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
    const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b);

    setColorFormats({
      hex,
      rgb,
      hsl,
      hsv
    });
  }, []);

  // 处理颜色变化
  const handleColorChange = useCallback((color: string) => {
    setCurrentColor(color);
    updateColorFormats(color);
    
    // 添加到历史记录
    if (!colorHistory.includes(color)) {
      setColorHistory(prev => [color, ...prev.slice(0, 11)]);
    }
  }, [colorHistory, updateColorFormats]);

  // 处理颜色代码转换
  const handleColorConversion = useCallback(() => {
    setConversionError("");
    
    if (!inputValue.trim()) {
      setConversionError("请输入颜色代码");
      return;
    }

    const parsedColor = parseColorInput(inputValue, inputFormat);
    
    if (parsedColor) {
      handleColorChange(parsedColor);
      setInputValue("");
      setConversionError("");
    } else {
      setConversionError(`${t('formats.error', {format: inputFormat.toUpperCase()})}`);
    }
  }, [inputValue, inputFormat, handleColorChange, parseColorInput]);

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert(`${t('formats.copied')} ${text}`);
    } catch {
      console.error("复制失败");
    }
  };

  // 生成随机颜色
  const generateRandomColor = () => {
    const randomColor = "#" + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
    handleColorChange(randomColor);
  };

  // 生成颜色变体
  const generateColorVariants = (baseColor: string) => {
    const rgb = hexToRgb(baseColor);
    const variants = [];
    
    // 亮度变体
    for (let i = 0; i < 5; i++) {
      const factor = 0.2 + (i * 0.2);
      const newR = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * factor));
      const newG = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * factor));
      const newB = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * factor));
      variants.push(rgbToHex(newR, newG, newB));
    }
    
    return variants;
  };

  // 初始化
  useEffect(() => {
    updateColorFormats(currentColor);
  }, [currentColor, updateColorFormats]);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/color-picker')}`,
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": locale === 'zh' ? "CNY" : "USD"
    },
    "featureList": locale === 'zh' ? [
      "颜色格式转换",
      "调色板选择",
      "颜色历史记录",
      "随机颜色生成"
    ] : [
      "Color format conversion",
      "Palette selection",
      "Color history",
      "Random color generation"
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-20">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                    {t('breadcrumb.tools')}
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            {/* 主要颜色选择区域 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mb-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 颜色选择器 */}
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {t('picker.title')}
                  </h2>
                  
                  {/* 颜色预览 */}
                  <div className="relative">
                    <div 
                      className="w-full h-32 rounded-lg border-4 border-white shadow-lg"
                      style={{ backgroundColor: currentColor }}
                    ></div>
                    <div className="absolute top-2 right-2">
                      <button
                        onClick={generateRandomColor}
                        className="px-3 py-1 bg-white/80 hover:bg-white text-gray-800 text-sm rounded-lg transition-colors duration-200"
                      >
                        🎲 {t('picker.random')}
                      </button>
                    </div>
                  </div>

                  {/* HTML颜色选择器 */}
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('picker.currentColor')}
                    </label>
                    <input
                      type="color"
                      value={currentColor}
                      onChange={(e) => handleColorChange(e.target.value)}
                      className="w-full h-12 rounded-lg border border-gray-300 dark:border-slate-600 cursor-pointer"
                    />
                  </div>

                  {/* HEX输入 */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('picker.hexCode')}
                    </label>
                    <input
                      type="text"
                      value={currentColor}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (/^#[0-9A-Fa-f]{0,6}$/.test(value)) {
                          if (value.length === 7) {
                            handleColorChange(value);
                          } else {
                            setCurrentColor(value);
                          }
                        }
                      }}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white font-mono"
                      placeholder="#3B82F6"
                    />
                  </div>
                </div>

                {/* 颜色格式显示 */}
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {t('formats.title')}
                  </h2>
                  
                  <div className="space-y-4">
                    {/* HEX */}
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">HEX</p>
                          <p className="text-lg font-mono text-gray-900 dark:text-white">{colorFormats.hex}</p>
                        </div>
                        <button
                          onClick={() => copyToClipboard(colorFormats.hex)}
                          className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors"
                        >
                          {t('formats.copy')}
                        </button>
                      </div>
                    </div>

                    {/* RGB */}
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">RGB</p>
                          <p className="text-lg font-mono text-gray-900 dark:text-white">
                            rgb({colorFormats.rgb.r}, {colorFormats.rgb.g}, {colorFormats.rgb.b})
                          </p>
                        </div>
                        <button
                          onClick={() => copyToClipboard(`rgb(${colorFormats.rgb.r}, ${colorFormats.rgb.g}, ${colorFormats.rgb.b})`)}
                          className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors"
                        >
                          {t('formats.copy')}
                        </button>
                      </div>
                    </div>

                    {/* HSL */}
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">HSL</p>
                          <p className="text-lg font-mono text-gray-900 dark:text-white">
                            hsl({colorFormats.hsl.h}, {colorFormats.hsl.s}%, {colorFormats.hsl.l}%)
                          </p>
                        </div>
                        <button
                          onClick={() => copyToClipboard(`hsl(${colorFormats.hsl.h}, ${colorFormats.hsl.s}%, ${colorFormats.hsl.l}%)`)}
                          className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors"
                        >
                          {t('formats.copy')}
                        </button>
                      </div>
                    </div>

                    {/* HSV */}
                    <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">HSV</p>
                          <p className="text-lg font-mono text-gray-900 dark:text-white">
                            hsv({colorFormats.hsv.h}, {colorFormats.hsv.s}%, {colorFormats.hsv.v}%)
                          </p>
                        </div>
                        <button
                          onClick={() => copyToClipboard(`hsv(${colorFormats.hsv.h}, ${colorFormats.hsv.s}%, ${colorFormats.hsv.v}%)`)}
                          className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors"
                        >
                          {t('formats.copy')}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 颜色代码转换工具 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {t('converter.title')}
              </h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* 格式选择 */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('converter.inputFormat')}
                  </label>
                  <select
                    value={inputFormat}
                    onChange={(e) => setInputFormat(e.target.value as "hex" | "rgb" | "hsl")}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                  >
                    <option value="hex">HEX (#FF0000)</option>
                    <option value="rgb">RGB (rgb(255, 0, 0))</option>
                    <option value="hsl">HSL (hsl(0, 100%, 50%))</option>
                  </select>
                </div>

                {/* 颜色代码输入 */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('converter.colorCode')}
                  </label>
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder={t(`converter.placeholders.${inputFormat}`)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white font-mono"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleColorConversion();
                      }
                    }}
                  />
                </div>

                {/* 转换按钮 */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('converter.convert')}
                  </label>
                  <button
                    onClick={handleColorConversion}
                    className="w-full px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 font-medium"
                  >
                    🔄 {t('converter.convert')}
                  </button>
                </div>
              </div>

              {/* 错误提示 */}
              {conversionError && (
                <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-red-600 dark:text-red-400">❌ {conversionError}</p>
                </div>
              )}

              {/* 格式示例 */}
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{t('formats.hex')} {t('examples.title')}</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1 font-mono">
                    <li>#FF0000</li>
                    <li>#f00</li>
                    <li>FF0000</li>
                    <li>f00</li>
                  </ul>
                </div>
                <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{t('formats.rgb')} {t('examples.title')}</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1 font-mono">
                    <li>rgb(255, 0, 0)</li>
                    <li>rgba(255, 0, 0, 1)</li>
                    <li>rgb(100, 150, 200)</li>
                  </ul>
                </div>
                <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{t('formats.hsl')} {t('examples.title')}</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1 font-mono">
                    <li>hsl(0, 100%, 50%)</li>
                    <li>hsla(0, 100%, 50%, 1)</li>
                    <li>hsl(240, 75%, 60%)</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 调色板 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {t('palettes.title')}
              </h2>
              
              {/* 调色板选择 */}
              <div className="flex flex-wrap gap-2 mb-6">
                {Object.keys(colorPalettes).map((palette) => (
                  <button
                    key={palette}
                    onClick={() => setSelectedPalette(palette as keyof typeof colorPalettes)}
                    className={`px-4 py-2 rounded-lg transition-colors ${
                      selectedPalette === palette
                        ? "bg-purple-600 text-white"
                        : "bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-600"
                    }`}
                  >
                    {t(`palettes.${palette}`)}
                  </button>
                ))}
              </div>

              {/* 颜色网格 */}
              <div className="grid grid-cols-6 md:grid-cols-12 gap-2">
                {colorPalettes[selectedPalette].map((color, index) => (
                  <button
                    key={index}
                    onClick={() => handleColorChange(color)}
                    className="w-12 h-12 rounded-lg border-2 border-white dark:border-slate-600 shadow-md hover:scale-110 transition-transform duration-200"
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>

            {/* 颜色变体 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {t('variants.title')}
              </h2>
              <div className="grid grid-cols-5 gap-4">
                {generateColorVariants(currentColor).map((variant, index) => (
                  <div key={index} className="text-center">
                    <button
                      onClick={() => handleColorChange(variant)}
                      className="w-full h-16 rounded-lg border-2 border-white dark:border-slate-600 shadow-md hover:scale-105 transition-transform duration-200 mb-2"
                      style={{ backgroundColor: variant }}
                    />
                    <p className="text-xs font-mono text-gray-600 dark:text-gray-400">{variant}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* 颜色历史 */}
            {colorHistory.length > 0 && (
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('history.title')}
                </h2>
                <div className="grid grid-cols-6 md:grid-cols-12 gap-2">
                  {colorHistory.map((color, index) => (
                    <button
                      key={index}
                      onClick={() => handleColorChange(color)}
                      className="w-12 h-12 rounded-lg border-2 border-white dark:border-slate-600 shadow-md hover:scale-110 transition-transform duration-200"
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* 使用说明 */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                {t('tips.title')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {t('tips.title')}
                  </h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    {[0, 1, 2, 3, 4, 5].map((index) => {
                      const item = t.raw(`tips.items.${index}`);
                      return item ? (
                        <li key={index} className="flex items-start">
                          <span className="text-purple-500 mr-2">✓</span>
                          {item}
                        </li>
                      ) : null;
                    }).filter(Boolean)}
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {t('usage.title')}
                  </h3>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    {[0, 1, 2, 3].map((index) => {
                      const item = t.raw(`usage.items.${index}`);
                      return item ? (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-500 mr-2">{index + 1}.</span>
                          {item}
                        </li>
                      ) : null;
                    }).filter(Boolean)}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}