import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { getAllPosts, getPostBySlug } from "@/lib/api";
import { SITE_NAME, SITE_URL, SITE_AUTHOR } from "@/lib/constants";
import { locales } from "@/i18n";
import markdownToHtml from "@/lib/markdownToHtml";

import Header from "@/app/_components/header";
import { PostBody } from "@/app/_components/post-body";
import { PostHeader } from "@/app/_components/post-header";
import { Comments } from "@/app/_components/comments";
import { StructuredData } from "@/app/_components/structured-data";


export default async function Post(props: ParamsWithLocale) {
  const params = await props.params;
  const post = getPostBySlug(params.slug, params.locale);

  if (!post) {
    return notFound();
  }

  const content = await markdownToHtml(post.content || "");

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 dark:from-slate-900 dark:via-blue-900/10 dark:to-purple-900/10 relative overflow-hidden">

      {/* 动态流动背景 */}
      <div className="fixed inset-0 -z-10 overflow-hidden will-change-transform">
        {/* 基础彩色背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-purple-50/15 to-pink-50/20 dark:from-blue-900/20 dark:via-purple-900/15 dark:to-pink-900/20"></div>

        {/* 主要流动渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-100/15 via-violet-100/10 to-rose-100/15 dark:from-cyan-800/15 dark:via-violet-800/10 dark:to-rose-800/15 animate-gradient-x"></div>

        {/* 流动的渐变球 */}
        <div className="absolute top-0 left-0 w-full h-full will-change-transform">
          {/* 大型流动球 1 */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-200/8 to-cyan-200/8 dark:from-blue-600/15 dark:to-cyan-600/15 rounded-full blur-3xl animate-float-slow opacity-30 will-change-transform"></div>

          {/* 大型流动球 2 */}
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-200/8 to-pink-200/8 dark:from-purple-600/15 dark:to-pink-600/15 rounded-full blur-3xl animate-float-reverse opacity-30 will-change-transform"></div>

          {/* 中型流动球 3 */}
          <div className="absolute top-1/2 left-3/4 w-64 h-64 bg-gradient-to-r from-emerald-200/6 to-teal-200/6 dark:from-emerald-600/12 dark:to-teal-600/12 rounded-full blur-2xl animate-float-diagonal opacity-25 will-change-transform"></div>

          {/* 小型流动球 4 */}
          <div className="absolute top-1/6 right-1/3 w-48 h-48 bg-gradient-to-r from-orange-200/6 to-yellow-200/6 dark:from-orange-600/12 dark:to-yellow-600/12 rounded-full blur-2xl animate-float-gentle opacity-20 will-change-transform"></div>

          {/* 小型流动球 5 */}
          <div className="absolute bottom-1/4 left-1/6 w-56 h-56 bg-gradient-to-r from-indigo-200/6 to-blue-200/6 dark:from-indigo-600/12 dark:to-blue-600/12 rounded-full blur-2xl animate-float-slow-reverse opacity-25 will-change-transform"></div>

          {/* 额外的小型装饰球 */}
          <div className="absolute top-1/8 left-1/2 w-32 h-32 bg-gradient-to-r from-rose-200/5 to-orange-200/5 dark:from-rose-600/10 dark:to-orange-600/10 rounded-full blur-xl animate-float-gentle opacity-15 will-change-transform"></div>
          <div className="absolute bottom-1/6 right-1/6 w-40 h-40 bg-gradient-to-r from-violet-200/5 to-indigo-200/5 dark:from-violet-600/10 dark:to-indigo-600/10 rounded-full blur-xl animate-float-slow opacity-20 will-change-transform"></div>
        </div>

        {/* 流动线条效果 */}
        <div className="absolute inset-0 opacity-10 will-change-transform">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-200/30 to-transparent animate-flow-horizontal"></div>
          <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-200/30 to-transparent animate-flow-horizontal-reverse"></div>
          <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-cyan-200/30 to-transparent animate-flow-vertical"></div>
          <div className="absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-pink-200/30 to-transparent animate-flow-vertical-reverse"></div>

          {/* 对角线流动效果 */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-emerald-200/20 to-transparent animate-flow-horizontal transform rotate-45 origin-left"></div>
          <div className="absolute bottom-0 right-0 w-full h-px bg-gradient-to-r from-transparent via-teal-200/20 to-transparent animate-flow-horizontal-reverse transform -rotate-45 origin-right"></div>
        </div>

        {/* 微妙的粒子效果 */}
        <div className="absolute inset-0 will-change-transform opacity-60">
          <div className="absolute top-1/5 left-1/6 w-1 h-1 bg-blue-200/30 rounded-full animate-particle-float"></div>
          <div className="absolute top-4/5 right-1/6 w-1 h-1 bg-purple-200/30 rounded-full animate-particle-float-delay"></div>
          <div className="absolute top-1/2 left-3/4 w-1 h-1 bg-cyan-200/30 rounded-full animate-particle-float-slow"></div>
          <div className="absolute top-1/8 right-1/2 w-1 h-1 bg-pink-200/30 rounded-full animate-particle-float-reverse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-1 h-1 bg-emerald-200/30 rounded-full animate-particle-float-gentle"></div>
          <div className="absolute top-3/8 right-1/8 w-1 h-1 bg-orange-200/25 rounded-full animate-particle-float"></div>
          <div className="absolute bottom-1/8 left-3/5 w-1 h-1 bg-violet-200/25 rounded-full animate-particle-float-delay"></div>
        </div>

        {/* 微妙的网格效果 */}
        <div className="absolute inset-0 opacity-5 dark:opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}></div>
        </div>
      </div>

      <div className="container-custom">
        <Header />
        <article className="mb-16 sm:mb-32">
          {/* 文章内容整合 */}
          <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-md rounded-xl sm:rounded-2xl border border-white/70 dark:border-slate-700/70 p-4 sm:p-6 lg:p-8 shadow-2xl shadow-purple-500/10 dark:shadow-purple-400/20">
            <PostHeader
              title={post.title}
              date={post.date}
              author={post.author}
              slug={post.slug}
            />

            <PostBody content={content} />

            {/* 评论区域 */}
            <Comments title={post.title} />
          </div>
        </article>

        {/* 文章结构化数据 */}
        <StructuredData
          type="article"
          title={post.title}
          description={post.excerpt}
          url={`${process.env.NEXT_PUBLIC_SITE_URL || 'https://lafucode.com'}/posts/${post.slug}`}
          datePublished={post.date}
          dateModified={post.lastModified || post.date}
          author={post.author?.name}
          image={post.ogImage?.url || post.coverImage}
        />


      </div>
    </main>
  );
}

type ParamsWithLocale = {
  params: Promise<{
    slug: string;
    locale: string;
  }>;
};

type Params = {
  params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata(props: ParamsWithLocale): Promise<Metadata> {
  const params = await props.params;
  const post = getPostBySlug(params.slug, params.locale);

  if (!post) {
    return notFound();
  }

  const title = `${post.title} | ${SITE_NAME}`;
  const description = post.excerpt || `阅读${post.title}，了解更多关于${post.title}的技术内容和实践经验。`;
  const ogImageUrl = post.ogImage?.url || post.coverImage;
  const articleUrl = `${SITE_URL}/posts/${post.slug}`;
  const publishedTime = new Date(post.date).toISOString();
  const modifiedTime = post.lastModified ? new Date(post.lastModified).toISOString() : publishedTime;

  // 从文章内容中提取关键词
  const extractKeywords = (content: string, title: string): string[] => {
    const baseKeywords = ["技术博客", "编程", "软件开发"];
    const titleWords = title.split(/[\s\-\|：:]+/).filter(word => word.length > 1);
    return [...baseKeywords, ...titleWords].slice(0, 10);
  };

  const keywords = extractKeywords(post.content || '', post.title);
  const authorName = post.author?.name || SITE_AUTHOR;

  return {
    title,
    description,
    keywords,
    authors: [{ name: authorName, url: SITE_URL }],
    creator: authorName,
    publisher: SITE_NAME,
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: params.locale === 'en' ? `/en/posts/${post.slug}` : `/posts/${post.slug}`,
      languages: {
        'zh': `/posts/${post.slug}`,
        'en': `/en/posts/${post.slug}`,
      },
    },
    openGraph: {
      type: "article",
      locale: "zh_CN",
      url: articleUrl,
      title: post.title,
      description,
      siteName: SITE_NAME,
      publishedTime,
      modifiedTime,
      authors: [authorName],
      section: "Technology",
      tags: keywords,
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description,
      images: [ogImageUrl],
      creator: `@${authorName}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    other: {
      "article:author": authorName,
      "article:published_time": publishedTime,
      "article:modified_time": modifiedTime,
      "article:section": "Technology",
      "article:tag": keywords.join(", "),
    },
  };
}

export async function generateStaticParams() {
  const paths: { locale: string; slug: string }[] = [];
  
  // 为每种语言生成文章路径
  for (const locale of locales) {
    const posts = getAllPosts(locale);
    const localePaths = posts.map((post) => ({
      locale,
      slug: post.slug,
    }));
    paths.push(...localePaths);
  }
  
  return paths;
}
