"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import dynamic from 'next/dynamic';

// 动态导入当前时间显示组件，禁用SSR
const CurrentTimeDisplay = dynamic(() => import('./components/CurrentTimeDisplay'), {
  ssr: false,
  loading: () => <div className="text-center"><p className="text-lg opacity-90">Loading current time...</p></div>
});

export default function TimestampPage() {
  const [timestamp, setTimestamp] = useState("");
  const [dateTime, setDateTime] = useState("");
  const [format, setFormat] = useState("seconds");

  const t = useTranslations('pages.tools.tools.timestamp');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };



  const timestampToDate = () => {
    try {
      const ts = parseInt(timestamp);
      if (isNaN(ts)) {
        alert(t('messages.invalidTimestamp'));
        return;
      }

      const date = format === "seconds" ? new Date(ts * 1000) : new Date(ts);
      setDateTime(date.toLocaleString(locale === 'zh' ? "zh-CN" : "en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        timeZoneName: "short"
      }));
    } catch {
      alert(t('messages.invalidTimestamp'));
    }
  };

  const dateToTimestamp = () => {
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) {
        alert(t('messages.invalidDateTime'));
        return;
      }

      const ts = format === "seconds"
        ? Math.floor(date.getTime() / 1000)
        : date.getTime();
      setTimestamp(ts.toString());
    } catch {
      alert(t('messages.invalidDateTime'));
    }
  };

  const useCurrentTime = () => {
    const now = new Date();
    const ts = format === "seconds"
      ? Math.floor(now.getTime() / 1000)
      : now.getTime();
    setTimestamp(ts.toString());
    setDateTime(now.toLocaleString(locale === 'zh' ? "zh-CN" : "en-US"));
  };

  const clearAll = () => {
    setTimestamp("");
    setDateTime("");
  };

  const copyTimestamp = async () => {
    try {
      await navigator.clipboard.writeText(timestamp);
      alert(t('messages.timestampCopied'));
    } catch {
      console.error("Copy failed");
    }
  };

  const copyDateTime = async () => {
    try {
      await navigator.clipboard.writeText(dateTime);
      alert(t('messages.dateTimeCopied'));
    } catch {
      console.error("Copy failed");
    }
  };

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": t('meta.title'),
            "description": t('meta.description'),
            "url": `${SITE_URL}${getLocalizedHref('/tools/timestamp')}`,
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "permissions": "browser",
            "isAccessibleForFree": true,
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": locale === 'zh' ? "CNY" : "USD"
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container-custom py-20">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                {t('breadcrumb.home')}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* Current Time Display */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl p-6 text-white mb-8">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4">{t('currentTime.title')}</h3>
            <CurrentTimeDisplay />
          </div>
        </div>

        {/* Format Selection */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-8">
          <div className="flex flex-wrap items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('controls.formatLabel')}
              </label>
              <div className="flex bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
                <button
                  onClick={() => setFormat("seconds")}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    format === "seconds"
                      ? "bg-indigo-600 text-white"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  {t('controls.seconds')}
                </button>
                <button
                  onClick={() => setFormat("milliseconds")}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    format === "milliseconds"
                      ? "bg-indigo-600 text-white"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  {t('controls.milliseconds')}
                </button>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            <button
              onClick={timestampToDate}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              {t('controls.timestampToDate')}
            </button>
            <button
              onClick={dateToTimestamp}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              {t('controls.dateToTimestamp')}
            </button>
            <button
              onClick={useCurrentTime}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              {t('controls.useCurrentTime')}
            </button>
            <button
              onClick={clearAll}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              {t('controls.clear')}
            </button>
          </div>
        </div>

        {/* Input/Output Areas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Timestamp Input */}
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('sections.timestamp')}
              </h3>
              {timestamp && (
                <button
                  onClick={copyTimestamp}
                  className="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 transition-colors"
                >
                  {t('sections.copy')}
                </button>
              )}
            </div>
            <input
              type="text"
              value={timestamp}
              onChange={(e) => setTimestamp(e.target.value)}
              placeholder={format === "seconds" ? t('placeholders.timestampSeconds') : t('placeholders.timestampMilliseconds')}
              className="w-full p-4 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white font-mono text-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {format === "seconds" ? t('descriptions.timestampSeconds') : t('descriptions.timestampMilliseconds')}
            </p>
          </div>

          {/* DateTime Input */}
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('sections.dateTime')}
              </h3>
              {dateTime && (
                <button
                  onClick={copyDateTime}
                  className="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 transition-colors"
                >
                  {t('sections.copy')}
                </button>
              )}
            </div>
            <input
              type="text"
              value={dateTime}
              onChange={(e) => setDateTime(e.target.value)}
              placeholder={t('placeholders.dateTime')}
              className="w-full p-4 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white font-mono text-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {t('descriptions.dateTimeFormats')}
            </p>
          </div>
        </div>

        {/* Usage Tips */}
        <div className="mt-12 bg-indigo-50 dark:bg-slate-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('tips.title')}
          </h3>
          <div className="space-y-4 text-gray-600 dark:text-gray-300">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('tips.timestampFormats.title')}</h4>
              <ul className="space-y-1 ml-4">
                {[0, 1].map((index) => {
                  const item = t.raw(`tips.timestampFormats.items.${index}`);
                  return item ? (
                    <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                  ) : null;
                }).filter(Boolean)}
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('tips.dateFormats.title')}</h4>
              <ul className="space-y-1 ml-4">
                {[0, 1, 2, 3].map((index) => {
                  const item = t.raw(`tips.dateFormats.items.${index}`);
                  return item ? (
                    <li key={index}>• {item}</li>
                  ) : null;
                }).filter(Boolean)}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}