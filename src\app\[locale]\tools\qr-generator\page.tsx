"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_URL } from "@/lib/constants";

// 导入组件
import ModeToggle from "./components/ModeToggle";
import QRTypeSelector from "./components/QRTypeSelector";
import QRContentInput from "./components/QRContentInput";
import QRStyleSettings from "./components/QRStyleSettings";
import QRPreview from "./components/QRPreview";
import BatchGenerator from "./components/BatchGenerator";

// 导入工具函数
import { generateQRCode, generateSVGQRCode, generateContentFromData } from "./utils/qrUtils";

export default function QRGeneratorPage() {
  const t = useTranslations('pages.tools.tools.qrGenerator');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 状态管理
  const [batchMode, setBatchMode] = useState(false);
  const [qrType, setQrType] = useState('text');
  const [textContent, setTextContent] = useState('');
  const [urlContent, setUrlContent] = useState('');
  const [wifiConfig, setWifiConfig] = useState({
    ssid: '',
    password: '',
    security: 'WPA',
    hidden: false
  });
  const [vcardInfo, setVcardInfo] = useState({
    name: '',
    phone: '',
    email: '',
    company: '',
    title: '',
    website: ''
  });
  const [qrSize, setQrSize] = useState(300);
  const [errorLevel, setErrorLevel] = useState('M');
  const [foregroundColor, setForegroundColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [batchData, setBatchData] = useState('');
  const [batchResults, setBatchResults] = useState<{content: string, dataUrl: string}[]>([]);

  // 生成内容
  const generateContent = useCallback(() => {
    return generateContentFromData(qrType, textContent, urlContent, wifiConfig, vcardInfo);
  }, [qrType, textContent, urlContent, wifiConfig, vcardInfo]);

  // 生成二维码
  const handleGenerateQR = useCallback(async () => {
    const content = generateContent();
    if (!content.trim()) return;

    setIsGenerating(true);
    try {
      const dataUrl = await generateQRCode(content, qrSize, errorLevel as 'L' | 'M' | 'Q' | 'H', foregroundColor, backgroundColor);
      setQrCodeDataUrl(dataUrl);
    } catch {
      console.error('Failed to generate QR code');
    } finally {
      setIsGenerating(false);
    }
  }, [generateContent, qrSize, errorLevel, foregroundColor, backgroundColor]);

  // 下载二维码
  const handleDownload = async (format: 'png' | 'svg') => {
    if (!qrCodeDataUrl && format === 'png') return;

    const link = document.createElement('a');
    link.download = `qrcode.${format}`;
    
    if (format === 'png') {
      link.href = qrCodeDataUrl!;
    } else {
      // 生成SVG格式
      const svgContent = await generateSVGQRCode(generateContent(), qrSize, errorLevel as 'L' | 'M' | 'Q' | 'H', foregroundColor, backgroundColor);
      const blob = new Blob([svgContent], { type: 'image/svg+xml' });
      link.href = URL.createObjectURL(blob);
    }
    
    link.click();
  };

  // 复制到剪贴板
  const handleCopyImage = async () => {
    if (!qrCodeDataUrl) return;
    
    try {
      const response = await fetch(qrCodeDataUrl);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      alert(t('messages.copySuccess'));
    } catch {
      console.error('Copy failed');
      alert(t('messages.copyFailed'));
    }
  };

  // 批量生成二维码
  const handleBatchGenerate = async () => {
    const lines = batchData.split('\n').filter(line => line.trim());
    if (lines.length === 0) return;

    setIsGenerating(true);
    const results: {content: string, dataUrl: string}[] = [];
    
    try {
      for (const line of lines) {
        await new Promise(resolve => setTimeout(resolve, 100)); // 避免阻塞UI
        const dataUrl = await generateQRCode(line.trim(), qrSize, errorLevel as 'L' | 'M' | 'Q' | 'H', foregroundColor, backgroundColor);
        if (dataUrl) {
          results.push({ content: line.trim(), dataUrl });
        }
      }
      setBatchResults(results);
    } catch {
      console.error('Batch generation failed');
    } finally {
      setIsGenerating(false);
    }
  };

  // 批量下载
  const handleBatchDownload = () => {
    batchResults.forEach((result, index) => {
      const link = document.createElement('a');
      link.download = `qrcode_${index + 1}.png`;
      link.href = result.dataUrl;
      link.click();
    });
  };

  // 自动生成二维码
  useEffect(() => {
    if (!batchMode) {
      const content = generateContent();
      if (content.trim()) {
        handleGenerateQR();
      }
    }
  }, [qrType, textContent, urlContent, wifiConfig, vcardInfo, qrSize, errorLevel, foregroundColor, backgroundColor, batchMode, generateContent, handleGenerateQR]);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/qr-generator')}`,
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "CNY"
    },
    "featureList": [
      t('features.text'),
      t('features.url'),
      t('features.wifi'),
      t('features.vcard'),
      t('features.customization'),
      t('features.formats')
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-12">
          {/* 面包屑导航 */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧：配置面板 */}
            <div className="space-y-6">
              {/* 模式切换 */}
              <ModeToggle batchMode={batchMode} setBatchMode={setBatchMode} />

              {batchMode ? (
                /* 批量模式界面 */
                <BatchGenerator
                  batchData={batchData}
                  setBatchData={setBatchData}
                  batchResults={batchResults}
                  isGenerating={isGenerating}
                  onBatchGenerate={handleBatchGenerate}
                  onBatchDownload={handleBatchDownload}
                />
              ) : (
                /* 单个模式界面 */
                <>
                  {/* 二维码类型选择 */}
                  <QRTypeSelector qrType={qrType} setQrType={setQrType} />

                  {/* 内容输入 */}
                  <QRContentInput
                    qrType={qrType}
                    textContent={textContent}
                    setTextContent={setTextContent}
                    urlContent={urlContent}
                    setUrlContent={setUrlContent}
                    wifiConfig={wifiConfig}
                    setWifiConfig={setWifiConfig}
                    vcardInfo={vcardInfo}
                    setVcardInfo={setVcardInfo}
                  />

                  {/* 样式设置 */}
                  <QRStyleSettings
                    qrSize={qrSize}
                    setQrSize={setQrSize}
                    errorLevel={errorLevel}
                    setErrorLevel={setErrorLevel}
                    foregroundColor={foregroundColor}
                    setForegroundColor={setForegroundColor}
                    backgroundColor={backgroundColor}
                    setBackgroundColor={setBackgroundColor}
                    showAdvanced={showAdvanced}
                    setShowAdvanced={setShowAdvanced}
                  />
                </>
              )}
            </div>

            {/* 右侧：预览和下载 */}
            {!batchMode && (
              <QRPreview
                qrCodeDataUrl={qrCodeDataUrl}
                qrSize={qrSize}
                isGenerating={isGenerating}
                onDownload={handleDownload}
                onCopyImage={handleCopyImage}
                generateContent={generateContent}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
}