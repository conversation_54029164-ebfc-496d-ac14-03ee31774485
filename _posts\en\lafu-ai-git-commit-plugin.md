---
featured: true
featuredOrder: 1
title: "Fed Up with GitHub Copilot's Quota Limits, I Built My Own Git Commit Message Generator Plugin"
excerpt: "GitHub Copilot consumes quota for generating commit messages? I spent a few weekends developing a completely free Git commit message generator plugin that supports local generation and multiple AI services - an intelligent commit assistant that never fails."
date: "2025-07-24"
author:
  name: "LaFu Code"
coverImage: "/assets/blog/28.png"
---

# Fed Up with GitHub Copilot's Quota Limits, I Built My Own Git Commit Message Generator Plugin

Recently, I've been using GitHub Copilot in company projects, and honestly, it's pretty useful. But there's one issue that really bothers me - every time I ask it to help generate Git commit messages, it consumes my quota.
![28.png](/assets/blog/28.png)

You know that feeling, right? You change one line of code, fix a small bug, and then you have to "waste" Copilot quota just to write a commit message. Especially at the end of the month when the quota is running low, every use hurts. Sometimes, just to save quota, I go back to the old way of writing commit messages manually.

As a programmer with terminal laziness, I couldn't stand this. Since <PERSON><PERSON><PERSON> can do it, why can't I build one myself? And make it even better!

## My Solution

After spending a few weekends, I created this **LaFu AI Git Commit** plugin. When I say a few weekends, the first weekend was mainly for writing the basic version, and the following weekends were all about optimization and adding features.

The biggest feature of this plugin is - **completely free by default**. No API key needed, just install and use. Of course, if you want smarter results, you can also integrate various AI services.

### Key Features

**Local Generation, No Cost**
Initially, I wondered if I could generate commit messages by purely analyzing code changes locally without relying on any external APIs. After studying the git diff output format, I found it was actually doable. While not as smart as AI, it's completely sufficient for daily commits.

**Support for Multiple AI Services**
Later, I thought since I'm already building this, why not add AI functionality too. I added support for OpenAI, Claude, Gemini, and domestic Tongyi Qianwen (which is more friendly for Chinese users).

**Never Fails**
This is my proudest design. AI service down? No problem, automatically switch to local generation. Bad network? No problem, local generation doesn't need internet. API quota exhausted? Still no problem, local generation is always free.

**Simple Operation**
Just a small button on the source control panel, one click and you're done. I've been using it for months and it's basically become muscle memory.

## How It's Implemented

### Local Generation Logic

Local generation is essentially analyzing git diff output. I wrote a simple algorithm:

- Count added and deleted lines
- See which files were changed
- Based on the scale of changes and file types, determine if it's feat, fix, or refactor
- Generate commit messages in Conventional Commits format

For example, if you modified `src/extension.ts` and added over 100 lines of code, it would generate:

```
feat: add new functionality to src/extension.ts

- Added 120 lines
- Deleted 15 lines
```

While not as smart as AI, it's definitely better than commit messages like `update` or `fix bug`.

### AI Generation Results

If you configure AI services, the results are significantly better. For the same changes, AI might generate:

```
feat: implement AI-driven commit message generation

Integrate multiple AI provider support, add intelligent fallback mechanism and configurable generation styles
```

Obviously more descriptive and professional.

### Configuration Options

I tried to keep configuration simple, but necessary options are still needed:

- Choose between Chinese or English commit messages
- Support different commit styles (I personally prefer Conventional Commits)
- Adjustable AI parameters, like creativity level
- API keys recommended to use environment variables for security

## Technical Details

### Multi-AI Support Implementation

To support different AI services, I created a unified interface. OpenAI and Tongyi Qianwen use the OpenAI SDK (Tongyi Qianwen supports OpenAI format), while Claude and Gemini call APIs directly.

```typescript
// Use unified SDK for OpenAI format support
if (config.provider === "openai" || config.provider === "tongyi") {
  return await callWithOpenAISDK(prompt, config);
}

// Use dedicated API calls for others
switch (config.provider) {
  case "claude":
    return await callClaude(prompt, config);
  case "gemini":
    return await callGemini(prompt, config);
}
```

### Prompt Design

This took quite a bit of time to debug. Different AIs understand prompts differently, and Chinese and English prompts need separate optimization. The final results are quite satisfactory.

### Error Handling

This is very important. AI services are often unstable, so I implemented extensive error handling:

- Automatically use local generation when AI fails
- Friendly timeout notifications
- Retry capability without re-operation

## How to Use

Installation is simple - just search "LaFu AI Git Commit" in the VS Code extension marketplace. Once installed, it works immediately without any configuration.

![29.png](/assets/blog/git-ai.png)

Daily usage:

1. After changing code, `git add .`
2. Open source control panel (Ctrl+Shift+G)
3. Click the little star button
4. Review the generated commit message, and commit if it looks good

I now use this for basically every commit, and it really saves a lot of time.

## User Feedback

Since release, I've received some great feedback. Some people say the local generation feature is very practical and eliminates network concerns. Chinese friends also appreciate the Tongyi Qianwen support, saying it generates more natural Chinese commit messages.

Of course, there are also improvement suggestions, which I've all noted in my TODO list.

## Future Plans

Several features under consideration:

- Custom template functionality, allowing users to define their own commit formats
- Team standard support, configurable team-level commit standards
- Learning functionality based on commit history (technically complex, still researching)
- Support for more AI services

But all of this depends on time, since it's a side project.

## Final Thoughts

This plugin solved my own pain point, and I hope it can help friends with similar frustrations. While the functionality isn't perfect yet, it should meet basic needs.

If you also struggle with writing Git commit messages or don't want to waste Copilot quota, give this plugin a try. For any questions or suggestions, feel free to raise issues on GitHub.

The code isn't particularly elegant, but it works. Open sourcing it in hopes that everyone can help improve it together.

---

**Project Repository**: [GitHub - lafucode-ai-git-commit](https://github.com/pythonsir/lafucode-ai-git-commit)

**VS Code Extension Marketplace**: Search "LaFu AI Git Commit"

**Website**: [https://lafucode.com](https://lafucode.com)

---

_If this article helps you, please like and share. If you encounter any issues during use, feel free to raise issues on GitHub - I'll respond and resolve them promptly._
