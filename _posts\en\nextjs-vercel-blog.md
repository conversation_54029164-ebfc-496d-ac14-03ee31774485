---
title: "Spent a Week Tinkering, Finally Got My Blog Running with Next.js"
excerpt: "Been wanting a personal blog for ages, tried Word<PERSON>ress and Hexo, but ended up choosing Next.js. Sharing my build process and the pitfalls I encountered."
coverImage: "/assets/blog/3.png"
date: "2025-07-01"
lastModified: "2025-07-01"
featured: true
featuredOrder: 2
featuredReason: "Programming best practices, valuable for developers"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
---

I've been wanting to build a personal blog for ages, kept procrastinating but finally got my hands dirty.

Previously tried WordPress (too heavy), used Hexo (themes were a pain to customize), and finally decided to roll my own with Next.js. Though it took a week, I'm pretty satisfied with the result.

Today I'm sharing my build process, hoping it helps fellow blog-wannabes.

## Why Choose Next.js + Vercel

Honestly, I chose this combo mainly because:

**Next.js advantages**:

- Based on React, which I'm already familiar with
- SSG (Static Site Generation) performs great for blogs
- Good developer experience, fast hot reload
- Active community, easy to find solutions when stuck

**Vercel advantages**:

- Deployment is ridiculously simple, just connect GitHub
- Free tier is enough for personal blogs
- Global CDN acceleration, fast access speeds
- Automatic HTTPS, worry-free

Most importantly, these two are from the same company, highly integrated, basically no compatibility issues.

## Getting Started

### Environment Setup

First make sure you have:

- Node.js (I'm using v18)
- Git
- A GitHub account

### Creating the Project

```bash
npx create-next-app@latest my-blog
```

During installation it'll ask you a bunch of questions, my choices were:

- TypeScript: Yes (highly recommended)
- ESLint: Yes
- Tailwind CSS: Yes (convenient for styling)
- src/ directory: Yes
- App Router: Yes (new routing approach)
- import alias: Yes

After creation:

```bash
cd my-blog
npm run dev
```

Open `http://localhost:3000`, seeing the Next.js welcome page means success.

### Designing Article Storage

I chose to store articles in Markdown files, reasons are simple:

- Good writing experience, focus on content
- Version control friendly
- Easy migration, not tied to specific platforms

Create a `_posts` folder in project root, then create your first article `hello-world.md`:

```markdown
---
title: "My First Blog Post"
date: "2025-07-01"
excerpt: "Finally got my own blog!"
---

This is my first blog post built with Next.js.

Though the process was a bit of a hassle, I'm quite satisfied with the result.
```

## Deploying to Vercel

This is the most satisfying part, really super simple:

1. **Push code to GitHub**

   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/your-username/my-blog.git
   git push -u origin main
   ```

2. **Connect Vercel**
   - Go to [vercel.com](https://vercel.com) and login with your GitHub account
   - Click "Add New..." → "Project"
   - Select your blog repository
   - Click "Deploy"

That's it, your blog goes live in a few minutes! Vercel gives you a `.vercel.app` domain.

## Pitfalls I Encountered

### Pitfall 1: Tailwind Styles Not Working

Initially my Tailwind styles never worked, later found out the `content` config in `tailwind.config.js` was wrong.

Correct config should be:

```javascript
content: [
  "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
  "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
  "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
],
```

### Pitfall 2: Ugly Article Content Styling

Markdown-converted HTML has no default styling, looks ugly. I used the `@tailwindcss/typography` plugin:

```bash
npm install @tailwindcss/typography
```

Then add to `tailwind.config.js`:

```javascript
plugins: [require("@tailwindcss/typography")],
```

Wrap article content with `prose` class for beautiful typography.

### Pitfall 3: 404 Pages After Deployment

Local development worked fine, but article detail pages 404'd after deployment. Reason was I didn't properly configure `generateStaticParams`, so pages weren't statically generated.

## Future Optimizations

Now the blog has basic functionality, but can be further optimized:

- **SEO optimization**: Add meta tags, sitemap
- **Comment system**: Consider integrating Giscus or Utterances
- **Search functionality**: Could use Algolia or simple client-side search
- **RSS feed**: Convenient for readers to subscribe
- **Dark mode**: Popular feature nowadays

## Summary

Building a blog with Next.js is indeed simpler than imagined, especially with Vercel's support. Though there's some learning curve upfront, the scalability and performance are excellent later on.

Most importantly, having your own blog makes writing more motivating. After all, a programmer not blogging is like a chef not cooking.

If you also want to build a personal blog, highly recommend trying this approach. Feel free to reach out if you have questions!
