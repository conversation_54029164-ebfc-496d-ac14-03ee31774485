---
excerpt: "Recently used Semi Design in a project and found it quite impressive. Here's my experience and some insights about this UI library open-sourced by ByteDance's team, along with what makes it special."
coverImage: "/assets/blog/25.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
featured: true
featuredOrder: 1
featuredReason: "In-depth analysis of modern React UI component library"
title: "ByteDance's Open Source Gem Semi Design: 3-Second Design-to-Code, Choice of 100K+ Developers"
date: "2025-07-19"
lastModified: "2025-07-19"
---

When our company started a new project recently, we needed to choose a UI library. We'd been using Ant Design for a while, but this time we wanted to try something different. I heard that ByteDance open-sourced Semi Design, which looked quite interesting, so we decided to give it a shot. After using it for some time, I found it does have some highlights, though we also encountered a few challenges. Here's my experience.

## First Impressions: Pretty Solid

Simply put, Semi Design is a React UI component library open-sourced by ByteDance's team. What sets it apart from other UI libraries is its focus on design-development collaboration.

Honestly, I started with a "let's just try it" attitude since we were comfortable with Ant Design. But after a few days of use, I discovered Semi does have some unique features:

1. **Good component quality** - Has all the essential components with modern styling
2. **Decent documentation** - At least clearer than some open-source project docs
3. **Flexible theme customization** - I'll elaborate on this later

Of course, there are some issues too, like the ecosystem isn't as mature as Ant Design's, and some third-party plugins might be hard to find.

## Some Interesting Features

### Design-to-Code

This feature is quite interesting, though I haven't used it in actual projects yet. It supposedly can generate code directly from Figma designs in just a few seconds.

I tried the demo, and it can indeed generate some simple layout code. To be honest, the generated code quality is still limited, and complex business logic definitely needs to be hand-written. But for rapid prototyping, this feature is quite practical.

### Architecture Design

Semi's architecture design is quite interesting, using the FA (Foundation & Adapter) pattern. Simply put, it separates core logic from the UI layer, theoretically making it easier to migrate to other frameworks.

However, for regular developers, this might not be very noticeable. It's mainly meaningful for library maintainers.

## Solid Accessibility Support

I have to praise this aspect - Semi has really put effort into accessibility support. Basic keyboard navigation and focus management are all there, and most components work properly with screen readers.

When using other UI libraries before, I often encountered keyboard navigation issues. Semi does this quite well. While you might not notice it day-to-day, it's very important for projects with accessibility requirements.

## Internationalization Support

If your project needs multi-language support, Semi's internationalization is quite good:

- Built-in translations for common languages
- RTL layout support (for Arabic, Hebrew, etc.)
- Comprehensive timezone handling

To be honest, most domestic projects might not need these features. But if you're building international projects, these can save a lot of work.

## Theme Customization: This is Really Sweet

This is what I consider Semi's biggest highlight. Theme customization is really well done, much more flexible than Ant Design.

### The Theme Editor is Great

Semi provides an online theme editor with real-time preview. I tried it - changing colors, font sizes, etc., and you can see the effects immediately. Plus, you can export theme packages to use directly in projects.

With Ant Design before, customizing a theme color required messing with various less variables for ages. Semi makes this much simpler - just point and click.

### Plenty of Official Themes

Semi officially provides several themes:

- Default Feishu theme (more business-oriented)
- ByteDance Creator Service theme (more youthful)
- CapCut theme (creative-oriented)

We ended up choosing the ByteDance Creator Service theme, which felt right for our product's tone.

## Some Technical Details

### SSR Support

Our project uses Next.js, and Semi has no issues with SSR. Previously with other UI libraries, we often encountered server-side rendering errors, but Semi is quite stable in this regard.

### Component Quality

The component quality feels solid after use - haven't encountered any obvious bugs. Updates are also frequent, and issues get fixed pretty quickly.

One thing to note: some of Semi's component APIs differ from Ant Design, so if you're used to Ant Design, you might need some adjustment time.

## Real-World Usage Experience

### Pitfalls Encountered

Let me share some issues I encountered during use:

1. **Ecosystem not mature enough** - Compared to Ant Design, Semi has fewer third-party plugins and extensions. For example, when I wanted an enhanced date range picker, I couldn't find a suitable one.

2. **API differences** - Some component APIs differ from Ant Design, requiring code changes during migration. But this isn't a big issue - just needs some adjustments.

3. **Documentation sometimes lacks detail** - Some advanced usage documentation isn't detailed enough, requiring source code inspection to understand.

### What I'm Satisfied With

Of course, there are many advantages:

1. **Theme customization is really convenient** - As mentioned before, much more convenient than other UI libraries.

2. **Modern component design** - Default styling is more modern than Ant Design, usable without much customization.

3. **Good performance** - Bundle size is well-controlled, and component rendering performance is fine.

## If You Want to Try It

If you want to try Semi Design, installation is simple:

```bash
npm install @douyinfe/semi-ui
```

Then you can start using it:

```jsx
import { Button, Toast } from "@douyinfe/semi-ui";

function App() {
  return <Button onClick={() => Toast.success("Hello Semi!")}>Click Me</Button>;
}
```

I recommend checking out the demos on the official website first to get a feel for the component styling and interactions. If it feels right, then consider using it in projects.

## Summary

After using Semi Design for a while, the overall experience is quite good. Especially the theme customization aspect - it's really much more convenient than other UI libraries. If your project has high requirements for theme customization, or if you want to try some new UI libraries, Semi Design is worth trying.

Of course, if your project is already using Ant Design smoothly without special requirements, there's no need to force a switch. After all, Ant Design's ecosystem is still more mature.

But for new projects, Semi Design is indeed a good choice. At least in our project, it's been quite smooth to use.

If you're interested, check out the [Semi Design official website](https://semi.design/) and try the demos before deciding whether to use it.